"use client"

import React from 'react'
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar'
import 'react-circular-progressbar/dist/styles.css'

interface ModernGaugeProps {
  value: number
  max: number
  size?: number
  className?: string
  title?: string
  subtitle?: string
  showPercentage?: boolean
}

export function ModernGauge({ 
  value, 
  max, 
  size = 200,
  className = "",
  title = "CO₂ Emissions",
  subtitle = "this month",
  showPercentage = true
}: ModernGaugeProps) {
  const percentage = Math.min((value / max) * 100, 100)
  
  // Dynamic color based on percentage using color theory
  const getColor = (percent: number) => {
    if (percent <= 25) return '#22c55e' // green-500 - excellent
    if (percent <= 50) return '#84cc16' // lime-500 - good
    if (percent <= 75) return '#f59e0b' // amber-500 - warning
    if (percent <= 90) return '#f97316' // orange-500 - high
    return '#ef4444' // red-500 - critical
  }

  const getGradientColors = (percent: number) => {
    if (percent <= 25) return { from: '#22c55e', to: '#16a34a' } // green gradient
    if (percent <= 50) return { from: '#84cc16', to: '#65a30d' } // lime gradient
    if (percent <= 75) return { from: '#f59e0b', to: '#d97706' } // amber gradient
    if (percent <= 90) return { from: '#f97316', to: '#ea580c' } // orange gradient
    return { from: '#ef4444', to: '#dc2626' } // red gradient
  }

  const color = getColor(percentage)
  const gradientColors = getGradientColors(percentage)

  return (
    <div className={`relative ${className}`} style={{ width: size, height: size }}>
      {/* SVG Definitions for Gradient */}
      <svg className="absolute inset-0" style={{ width: 0, height: 0 }}>
        <defs>
          <linearGradient id={`gauge-gradient-${size}`} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor={gradientColors.from} />
            <stop offset="100%" stopColor={gradientColors.to} />
          </linearGradient>
        </defs>
      </svg>

      {/* Main circular progress */}
      <CircularProgressbar
        value={percentage}
        styles={buildStyles({
          // Rotation of path and trail, in number of turns (0-1)
          rotation: 0.25,

          // Whether to use rounded or flat corners on the ends
          strokeLinecap: 'round',

          // Text size
          textSize: '0px', // We'll use custom text

          // How long animation takes to go from one percentage to another, in seconds
          pathTransitionDuration: 1.5,

          // Colors
          pathColor: `url(#gauge-gradient-${size})`,
          textColor: color,
          trailColor: 'rgba(255, 255, 255, 0.2)',
          backgroundColor: 'transparent',
        })}
        strokeWidth={10}
      />
      
      {/* Custom center content */}
      <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
        {/* Main value */}
        <div className="text-center mb-1">
          <div className={`font-bold ${size > 180 ? 'text-3xl' : size > 140 ? 'text-2xl' : 'text-xl'}`}>
            {value}
          </div>
          <div className={`text-white/80 ${size > 180 ? 'text-sm' : 'text-xs'}`}>
            kg {title}
          </div>
        </div>
        
        {/* Percentage indicator */}
        {showPercentage && (
          <div className="text-center mt-2">
            <div
              className={`font-bold ${size > 180 ? 'text-lg' : 'text-base'} text-white drop-shadow-lg`}
              style={{
                textShadow: '0 2px 4px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.8)',
                filter: 'contrast(1.2)'
              }}
            >
              {percentage.toFixed(0)}%
            </div>
            <div className={`text-white/70 ${size > 180 ? 'text-xs' : 'text-xs'}`}>
              of {max}kg target
            </div>
          </div>
        )}
        
        {/* Subtitle */}
        <div className={`text-white/60 text-center mt-1 ${size > 180 ? 'text-xs' : 'text-xs'}`}>
          {subtitle}
        </div>
      </div>
      
      {/* Glow effect */}
      <div 
        className="absolute inset-0 rounded-full opacity-20 blur-xl"
        style={{ 
          background: `radial-gradient(circle, ${color}40 0%, transparent 70%)`,
          transform: 'scale(1.2)'
        }}
      />
    </div>
  )
}
