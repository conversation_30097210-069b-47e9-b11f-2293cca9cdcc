"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/journey-history.tsx":
/*!****************************************!*\
  !*** ./components/journey-history.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JourneyHistory: () => (/* binding */ JourneyHistory)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mock-journeys */ \"(app-pages-browser)/./lib/mock-journeys.ts\");\n/* harmony import */ var _barrel_optimize_names_FiActivity_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiActivity,FiCalendar,FiChevronLeft,FiChevronRight!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ JourneyHistory auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction JourneyHistory(param) {\n    let { journeys, className = \"\" } = param;\n    _s();\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('daily');\n    const [currentDate, setCurrentDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const periodButtons = [\n        {\n            period: 'daily',\n            label: 'Daily'\n        },\n        {\n            period: 'weekly',\n            label: 'Weekly'\n        },\n        {\n            period: 'monthly',\n            label: 'Monthly'\n        },\n        {\n            period: 'yearly',\n            label: 'Yearly'\n        }\n    ];\n    const filteredJourneys = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"JourneyHistory.useMemo[filteredJourneys]\": ()=>(0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__.filterJourneysByPeriod)(journeys, selectedPeriod, currentDate)\n    }[\"JourneyHistory.useMemo[filteredJourneys]\"], [\n        journeys,\n        selectedPeriod,\n        currentDate\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"JourneyHistory.useMemo[stats]\": ()=>(0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__.calculateJourneyStats)(filteredJourneys)\n    }[\"JourneyHistory.useMemo[stats]\"], [\n        filteredJourneys\n    ]);\n    const navigateDate = (direction)=>{\n        const newDate = new Date(currentDate);\n        switch(selectedPeriod){\n            case 'daily':\n                newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));\n                break;\n            case 'weekly':\n                newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));\n                break;\n            case 'monthly':\n                newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));\n                break;\n            case 'yearly':\n                newDate.setFullYear(newDate.getFullYear() + (direction === 'next' ? 1 : -1));\n                break;\n        }\n        setCurrentDate(newDate);\n    };\n    const formatJourneyTime = (timestamp)=>{\n        return timestamp.toLocaleTimeString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    const formatJourneyDate = (timestamp)=>{\n        if (selectedPeriod === 'daily') {\n            return formatJourneyTime(timestamp);\n        }\n        return timestamp.toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            ...selectedPeriod === 'yearly' ? {\n                year: 'numeric'\n            } : {}\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-glass p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiActivity_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCalendar, {\n                                        className: \"w-5 h-5 text-emerald-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-800\",\n                                        children: \"Journey History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: periodButtons.map((param)=>{\n                                    let { period, label } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: selectedPeriod === period ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedPeriod(period),\n                                        className: \"\".concat(selectedPeriod === period ? 'bg-emerald-500 text-white' : 'bg-white/60 text-gray-700 border-gray-300'),\n                                        children: label\n                                    }, period, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-4 pt-4 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>navigateDate('prev'),\n                                className: \"bg-white/60 border-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiActivity_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiChevronLeft, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800\",\n                                    children: (0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__.formatDateForPeriod)(currentDate, selectedPeriod)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>navigateDate('next'),\n                                className: \"bg-white/60 border-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiActivity_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiChevronRight, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 sm:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-emerald-600\",\n                                children: stats.totalJourneys\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Journeys\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-600\",\n                                children: stats.totalDistance\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"km Total\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-orange-600\",\n                                children: stats.totalEmissions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"kg CO₂\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-purple-600\",\n                                children: stats.averageEmissions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Avg CO₂\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-glass p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiActivity_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiActivity, {\n                                className: \"w-5 h-5 text-emerald-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800\",\n                                children: [\n                                    filteredJourneys.length,\n                                    \" Journey\",\n                                    filteredJourneys.length !== 1 ? 's' : ''\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    filteredJourneys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl mb-2\",\n                                children: \"\\uD83C\\uDF31\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No journeys recorded for this period\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Add a journey to start tracking!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                        children: filteredJourneys.map((journey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full \".concat(journey.transportColor, \" flex items-center justify-center text-white text-lg flex-shrink-0\"),\n                                                children: journey.transportIcon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-800\",\n                                                                children: journey.transportName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    journey.distance,\n                                                                    \" km\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: formatJourneyDate(journey.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right flex-shrink-0 ml-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-semibold text-emerald-600\",\n                                                children: [\n                                                    journey.emissions,\n                                                    \" kg\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"CO₂\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, journey.id, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(JourneyHistory, \"haPjCueP3H1rILyppO5CUx3tOAo=\");\n_c = JourneyHistory;\nvar _c;\n$RefreshReg$(_c, \"JourneyHistory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/journey-history.tsx\n"));

/***/ })

});