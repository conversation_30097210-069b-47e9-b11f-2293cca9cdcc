"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/mock-journeys.ts":
/*!******************************!*\
  !*** ./lib/mock-journeys.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateJourneyStats: () => (/* binding */ calculateJourneyStats),\n/* harmony export */   filterJourneysByPeriod: () => (/* binding */ filterJourneysByPeriod),\n/* harmony export */   formatDateForPeriod: () => (/* binding */ formatDateForPeriod),\n/* harmony export */   generateMockJourneys: () => (/* binding */ generateMockJourneys),\n/* harmony export */   transportModes: () => (/* binding */ transportModes)\n/* harmony export */ });\n// Simple seeded random number generator for consistent results\nclass SeededRandom {\n    next() {\n        this.seed = (this.seed * 9301 + 49297) % 233280;\n        return this.seed / 233280;\n    }\n    constructor(seed){\n        this.seed = seed;\n    }\n}\nconst transportModes = [\n    {\n        id: 'bicycle',\n        name: 'Bicycle',\n        icon: '🚲',\n        color: 'bg-blue-400',\n        emissionFactor: 0\n    },\n    {\n        id: 'walking',\n        name: 'Walking',\n        icon: '🚶',\n        color: 'bg-green-400',\n        emissionFactor: 0\n    },\n    {\n        id: 'bus',\n        name: 'Bus',\n        icon: '🚌',\n        color: 'bg-yellow-400',\n        emissionFactor: 0.089\n    },\n    {\n        id: 'train',\n        name: 'Train',\n        icon: '🚊',\n        color: 'bg-orange-400',\n        emissionFactor: 0.041\n    },\n    {\n        id: 'motorbike',\n        name: 'Motorbike',\n        icon: '🏍️',\n        color: 'bg-red-400',\n        emissionFactor: 0.113\n    },\n    {\n        id: 'car',\n        name: 'Car',\n        icon: '🚗',\n        color: 'bg-gray-400',\n        emissionFactor: 0.171\n    },\n    {\n        id: 'plane',\n        name: 'Plane',\n        icon: '✈️',\n        color: 'bg-purple-400',\n        emissionFactor: 0.255\n    }\n];\n// Generate mock journeys for different time periods\nfunction generateMockJourneys() {\n    const journeys = [];\n    const rng = new SeededRandom(12345) // Fixed seed for consistent results\n    ;\n    const baseDate = new Date('2024-01-01') // Fixed base date\n    ;\n    const now = new Date();\n    // Generate journeys for the past 365 days from today\n    for(let i = 0; i < 365; i++){\n        const date = new Date(now);\n        date.setDate(date.getDate() - i);\n        // Seeded random number of journeys per day (0-4)\n        const journeysPerDay = Math.floor(rng.next() * 5);\n        for(let j = 0; j < journeysPerDay; j++){\n            const transport = transportModes[Math.floor(rng.next() * transportModes.length)];\n            const distance = Math.floor(rng.next() * 50) + 1 // 1-50 km\n            ;\n            const emissions = transport.emissionFactor * distance;\n            // Seeded random time during the day\n            const hour = Math.floor(rng.next() * 24);\n            const minute = Math.floor(rng.next() * 60);\n            const journeyTime = new Date(date);\n            journeyTime.setHours(hour, minute, 0, 0);\n            journeys.push({\n                id: \"journey-\".concat(i, \"-\").concat(j),\n                transportMode: transport.id,\n                transportName: transport.name,\n                transportIcon: transport.icon,\n                transportColor: transport.color,\n                distance,\n                emissions: emissions === 0 ? 0 : Number(emissions.toFixed(1)),\n                timestamp: journeyTime,\n                vehicleType: transport.id === 'car' ? 'medium' : transport.id === 'motorbike' ? '250cc' : undefined\n            });\n        }\n    }\n    return journeys.sort((a, b)=>b.timestamp.getTime() - a.timestamp.getTime());\n}\n// Filter journeys by time period\nfunction filterJourneysByPeriod(journeys, period) {\n    let date = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : new Date();\n    const startDate = new Date(date);\n    const endDate = new Date(date);\n    switch(period){\n        case 'daily':\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'weekly':\n            const dayOfWeek = startDate.getDay();\n            startDate.setDate(startDate.getDate() - dayOfWeek);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setDate(startDate.getDate() + 6);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'monthly':\n            startDate.setDate(1);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setMonth(endDate.getMonth() + 1, 0);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'yearly':\n            startDate.setMonth(0, 1);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setMonth(11, 31);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n    }\n    return journeys.filter((journey)=>journey.timestamp >= startDate && journey.timestamp <= endDate);\n}\n// Calculate journey statistics\nfunction calculateJourneyStats(journeys) {\n    const totalJourneys = journeys.length;\n    const totalDistance = journeys.reduce((sum, journey)=>sum + journey.distance, 0);\n    const totalEmissions = journeys.reduce((sum, journey)=>sum + journey.emissions, 0);\n    const averageEmissions = totalJourneys > 0 ? totalEmissions / totalJourneys : 0;\n    return {\n        totalJourneys,\n        totalDistance,\n        totalEmissions: Number(totalEmissions.toFixed(2)),\n        averageEmissions: Number(averageEmissions.toFixed(2))\n    };\n}\n// Format date for display\nfunction formatDateForPeriod(date, period) {\n    switch(period){\n        case 'daily':\n            return date.toLocaleDateString('en-US', {\n                weekday: 'long',\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n            });\n        case 'weekly':\n            const weekStart = new Date(date);\n            const dayOfWeek = weekStart.getDay();\n            weekStart.setDate(weekStart.getDate() - dayOfWeek);\n            const weekEnd = new Date(weekStart);\n            weekEnd.setDate(weekEnd.getDate() + 6);\n            return \"\".concat(weekStart.toLocaleDateString('en-US', {\n                month: 'short',\n                day: 'numeric'\n            }), \" - \").concat(weekEnd.toLocaleDateString('en-US', {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric'\n            }));\n        case 'monthly':\n            return date.toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long'\n            });\n        case 'yearly':\n            return date.getFullYear().toString();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-journeys.ts\n"));

/***/ })

});