"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/journey-history.tsx":
/*!****************************************!*\
  !*** ./components/journey-history.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JourneyHistory: () => (/* binding */ JourneyHistory)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mock-journeys */ \"(app-pages-browser)/./lib/mock-journeys.ts\");\n/* harmony import */ var _barrel_optimize_names_FiActivity_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiActivity,FiCalendar,FiChevronLeft,FiChevronRight!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ JourneyHistory auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction JourneyHistory(param) {\n    let { journeys, className = \"\" } = param;\n    _s();\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('daily');\n    const [currentDate, setCurrentDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const periodButtons = [\n        {\n            period: 'daily',\n            label: 'Daily'\n        },\n        {\n            period: 'weekly',\n            label: 'Weekly'\n        },\n        {\n            period: 'monthly',\n            label: 'Monthly'\n        },\n        {\n            period: 'yearly',\n            label: 'Yearly'\n        }\n    ];\n    const filteredJourneys = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"JourneyHistory.useMemo[filteredJourneys]\": ()=>(0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__.filterJourneysByPeriod)(journeys, selectedPeriod, currentDate)\n    }[\"JourneyHistory.useMemo[filteredJourneys]\"], [\n        journeys,\n        selectedPeriod,\n        currentDate\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"JourneyHistory.useMemo[stats]\": ()=>(0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__.calculateJourneyStats)(filteredJourneys)\n    }[\"JourneyHistory.useMemo[stats]\"], [\n        filteredJourneys\n    ]);\n    const navigateDate = (direction)=>{\n        const newDate = new Date(currentDate);\n        switch(selectedPeriod){\n            case 'daily':\n                newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));\n                break;\n            case 'weekly':\n                newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));\n                break;\n            case 'monthly':\n                newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));\n                break;\n            case 'yearly':\n                newDate.setFullYear(newDate.getFullYear() + (direction === 'next' ? 1 : -1));\n                break;\n        }\n        setCurrentDate(newDate);\n    };\n    const formatJourneyTime = (timestamp)=>{\n        return timestamp.toLocaleTimeString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    const formatJourneyDate = (timestamp)=>{\n        if (selectedPeriod === 'daily') {\n            return formatJourneyTime(timestamp);\n        }\n        return timestamp.toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            ...selectedPeriod === 'yearly' ? {\n                year: 'numeric'\n            } : {}\n        });\n    };\n    const formatEmissions = (emissions)=>{\n        if (emissions === 0) return '0';\n        if (emissions < 0.1) return '<0.1';\n        return emissions.toFixed(1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-glass p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiActivity_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCalendar, {\n                                        className: \"w-5 h-5 text-emerald-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-800\",\n                                        children: \"Journey History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: periodButtons.map((param)=>{\n                                    let { period, label } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: selectedPeriod === period ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedPeriod(period),\n                                        className: \"\".concat(selectedPeriod === period ? 'bg-emerald-500 text-white' : 'bg-white/60 text-gray-700 border-gray-300'),\n                                        children: label\n                                    }, period, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-4 pt-4 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>navigateDate('prev'),\n                                className: \"bg-white/60 border-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiActivity_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiChevronLeft, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800\",\n                                    children: (0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__.formatDateForPeriod)(currentDate, selectedPeriod)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>navigateDate('next'),\n                                className: \"bg-white/60 border-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiActivity_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiChevronRight, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 sm:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-emerald-600\",\n                                children: stats.totalJourneys\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Journeys\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-600\",\n                                children: stats.totalDistance\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"km Total\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-orange-600\",\n                                children: stats.totalEmissions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"kg CO₂\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-purple-600\",\n                                children: stats.averageEmissions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Avg CO₂\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-glass p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiActivity_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiActivity, {\n                                className: \"w-5 h-5 text-emerald-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800\",\n                                children: [\n                                    filteredJourneys.length,\n                                    \" Journey\",\n                                    filteredJourneys.length !== 1 ? 's' : ''\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    filteredJourneys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl mb-2\",\n                                children: \"\\uD83C\\uDF31\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No journeys recorded for this period\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Add a journey to start tracking!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                        children: filteredJourneys.map((journey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full \".concat(journey.transportColor, \" flex items-center justify-center text-white text-lg flex-shrink-0\"),\n                                                children: journey.transportIcon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-800\",\n                                                                children: journey.transportName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    journey.distance,\n                                                                    \" km\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: formatJourneyDate(journey.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right flex-shrink-0 ml-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-semibold text-emerald-600\",\n                                                children: [\n                                                    journey.emissions,\n                                                    \" kg\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"CO₂\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, journey.id, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(JourneyHistory, \"haPjCueP3H1rILyppO5CUx3tOAo=\");\n_c = JourneyHistory;\nvar _c;\n$RefreshReg$(_c, \"JourneyHistory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/journey-history.tsx\n"));

/***/ })

});