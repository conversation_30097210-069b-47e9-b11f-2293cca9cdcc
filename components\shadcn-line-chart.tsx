"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

interface ConsumptionData {
  month: string
  value: number
}

interface ShadcnLineChartProps {
  data: ConsumptionData[]
  className?: string
}

const chartConfig = {
  value: {
    label: "CO₂ Emissions",
    color: "hsl(var(--chart-1))",
  },
} satisfies ChartConfig

export function ShadcnLineChart({ data, className = "" }: ShadcnLineChartProps) {
  return (
    <div className={`p-4 sm:p-6 ${className}`}>
      <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4 sm:mb-6">Consumption Trend</h3>

      <ChartContainer config={chartConfig} className="min-h-[200px] sm:min-h-[250px] lg:min-h-[280px] w-full">
        <LineChart
          accessibilityLayer
          data={data}
          margin={{
            left: 16,
            right: 16,
            top: 16,
            bottom: 16,
          }}
        >
          <CartesianGrid
            vertical={false}
            strokeDasharray="3 3"
            stroke="#e5e7eb"
            opacity={0.6}
          />
          <XAxis
            dataKey="month"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tick={{ fontSize: 11, fill: '#6b7280' }}
            tickFormatter={(value) => value.slice(0, 3)}
          />
          <YAxis
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tick={{ fontSize: 11, fill: '#6b7280' }}
            tickFormatter={(value) => `${value}kg`}
            width={50}
          />
          <ChartTooltip
            cursor={{ stroke: '#10b981', strokeWidth: 1, strokeDasharray: '3 3' }}
            content={<ChartTooltipContent
              hideLabel
              className="bg-white/95 backdrop-blur-sm border border-emerald-200 rounded-xl shadow-lg"
            />}
          />
          <Line
            dataKey="value"
            type="monotone"
            stroke="#10b981"
            strokeWidth={3}
            dot={{
              fill: "#10b981",
              strokeWidth: 2,
              r: 4,
            }}
            activeDot={{
              r: 6,
              strokeWidth: 2,
              fill: "#059669",
            }}
          />
        </LineChart>
      </ChartContainer>

      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
            <span className="text-sm text-gray-600 font-medium">Your Emissions</span>
          </div>
          <div className="text-right">
            <span className="text-lg font-bold text-emerald-600">
              {data[data.length - 1]?.value || 0} kg
            </span>
            <span className="text-sm text-gray-500 ml-1">CO₂</span>
          </div>
        </div>

        <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
          <span className="text-xs text-gray-500">Global Average</span>
          <span className="text-sm font-semibold text-gray-600">760kg CO₂</span>
        </div>
      </div>
    </div>
  )
}
