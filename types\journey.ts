export interface Journey {
  id: string
  transportMode: string
  transportName: string
  transportIcon: string
  transportColor: string
  distance: number
  emissions: number
  timestamp: Date
  vehicleType?: string
}

export interface TransportMode {
  id: string
  name: string
  icon: string
  color: string
  emissionFactor: number // kg CO2 per km
}

export type TimePeriod = 'daily' | 'weekly' | 'monthly' | 'yearly'

export interface JourneyStats {
  totalJourneys: number
  totalDistance: number
  totalEmissions: number
  averageEmissions: number
}
