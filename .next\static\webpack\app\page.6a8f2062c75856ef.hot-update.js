"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/mock-journeys.ts":
/*!******************************!*\
  !*** ./lib/mock-journeys.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateJourneyStats: () => (/* binding */ calculateJourneyStats),\n/* harmony export */   filterJourneysByPeriod: () => (/* binding */ filterJourneysByPeriod),\n/* harmony export */   formatDateForPeriod: () => (/* binding */ formatDateForPeriod),\n/* harmony export */   generateMockJourneys: () => (/* binding */ generateMockJourneys),\n/* harmony export */   transportModes: () => (/* binding */ transportModes)\n/* harmony export */ });\n// Simple seeded random number generator for consistent results\nclass SeededRandom {\n    next() {\n        this.seed = (this.seed * 9301 + 49297) % 233280;\n        return this.seed / 233280;\n    }\n    constructor(seed){\n        this.seed = seed;\n    }\n}\nconst transportModes = [\n    {\n        id: 'bicycle',\n        name: 'Bicycle',\n        icon: '🚲',\n        color: 'bg-blue-400',\n        emissionFactor: 0\n    },\n    {\n        id: 'walking',\n        name: 'Walking',\n        icon: '🚶',\n        color: 'bg-green-400',\n        emissionFactor: 0\n    },\n    {\n        id: 'bus',\n        name: 'Bus',\n        icon: '🚌',\n        color: 'bg-yellow-400',\n        emissionFactor: 0.089\n    },\n    {\n        id: 'train',\n        name: 'Train',\n        icon: '🚊',\n        color: 'bg-orange-400',\n        emissionFactor: 0.041\n    },\n    {\n        id: 'motorbike',\n        name: 'Motorbike',\n        icon: '🏍️',\n        color: 'bg-red-400',\n        emissionFactor: 0.113\n    },\n    {\n        id: 'car',\n        name: 'Car',\n        icon: '🚗',\n        color: 'bg-gray-400',\n        emissionFactor: 0.171\n    },\n    {\n        id: 'plane',\n        name: 'Plane',\n        icon: '✈️',\n        color: 'bg-purple-400',\n        emissionFactor: 0.255\n    }\n];\n// Generate mock journeys for different time periods\nfunction generateMockJourneys() {\n    const journeys = [];\n    const now = new Date();\n    // Generate journeys for the past 365 days\n    for(let i = 0; i < 365; i++){\n        const date = new Date(now);\n        date.setDate(date.getDate() - i);\n        // Random number of journeys per day (0-4)\n        const journeysPerDay = Math.floor(Math.random() * 5);\n        for(let j = 0; j < journeysPerDay; j++){\n            const transport = transportModes[Math.floor(Math.random() * transportModes.length)];\n            const distance = Math.floor(Math.random() * 50) + 1 // 1-50 km\n            ;\n            const emissions = transport.emissionFactor * distance;\n            // Random time during the day\n            const hour = Math.floor(Math.random() * 24);\n            const minute = Math.floor(Math.random() * 60);\n            const journeyTime = new Date(date);\n            journeyTime.setHours(hour, minute, 0, 0);\n            journeys.push({\n                id: \"journey-\".concat(i, \"-\").concat(j, \"-\").concat(Date.now()),\n                transportMode: transport.id,\n                transportName: transport.name,\n                transportIcon: transport.icon,\n                transportColor: transport.color,\n                distance,\n                emissions: Number(emissions.toFixed(2)),\n                timestamp: journeyTime,\n                vehicleType: transport.id === 'car' ? 'medium' : transport.id === 'motorbike' ? '250cc' : undefined\n            });\n        }\n    }\n    return journeys.sort((a, b)=>b.timestamp.getTime() - a.timestamp.getTime());\n}\n// Filter journeys by time period\nfunction filterJourneysByPeriod(journeys, period) {\n    let date = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : new Date();\n    const startDate = new Date(date);\n    const endDate = new Date(date);\n    switch(period){\n        case 'daily':\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'weekly':\n            const dayOfWeek = startDate.getDay();\n            startDate.setDate(startDate.getDate() - dayOfWeek);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setDate(startDate.getDate() + 6);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'monthly':\n            startDate.setDate(1);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setMonth(endDate.getMonth() + 1, 0);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'yearly':\n            startDate.setMonth(0, 1);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setMonth(11, 31);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n    }\n    return journeys.filter((journey)=>journey.timestamp >= startDate && journey.timestamp <= endDate);\n}\n// Calculate journey statistics\nfunction calculateJourneyStats(journeys) {\n    const totalJourneys = journeys.length;\n    const totalDistance = journeys.reduce((sum, journey)=>sum + journey.distance, 0);\n    const totalEmissions = journeys.reduce((sum, journey)=>sum + journey.emissions, 0);\n    const averageEmissions = totalJourneys > 0 ? totalEmissions / totalJourneys : 0;\n    return {\n        totalJourneys,\n        totalDistance,\n        totalEmissions: Number(totalEmissions.toFixed(2)),\n        averageEmissions: Number(averageEmissions.toFixed(2))\n    };\n}\n// Format date for display\nfunction formatDateForPeriod(date, period) {\n    switch(period){\n        case 'daily':\n            return date.toLocaleDateString('en-US', {\n                weekday: 'long',\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n            });\n        case 'weekly':\n            const weekStart = new Date(date);\n            const dayOfWeek = weekStart.getDay();\n            weekStart.setDate(weekStart.getDate() - dayOfWeek);\n            const weekEnd = new Date(weekStart);\n            weekEnd.setDate(weekEnd.getDate() + 6);\n            return \"\".concat(weekStart.toLocaleDateString('en-US', {\n                month: 'short',\n                day: 'numeric'\n            }), \" - \").concat(weekEnd.toLocaleDateString('en-US', {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric'\n            }));\n        case 'monthly':\n            return date.toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long'\n            });\n        case 'yearly':\n            return date.getFullYear().toString();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9tb2NrLWpvdXJuZXlzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRUEsK0RBQStEO0FBQy9ELE1BQU1BO0lBT0pDLE9BQWU7UUFDYixJQUFJLENBQUNDLElBQUksR0FBRyxDQUFDLElBQUksQ0FBQ0EsSUFBSSxHQUFHLE9BQU8sS0FBSSxJQUFLO1FBQ3pDLE9BQU8sSUFBSSxDQUFDQSxJQUFJLEdBQUc7SUFDckI7SUFQQSxZQUFZQSxJQUFZLENBQUU7UUFDeEIsSUFBSSxDQUFDQSxJQUFJLEdBQUdBO0lBQ2Q7QUFNRjtBQUVPLE1BQU1DLGlCQUFrQztJQUM3QztRQUFFQyxJQUFJO1FBQVdDLE1BQU07UUFBV0MsTUFBTTtRQUFNQyxPQUFPO1FBQWVDLGdCQUFnQjtJQUFFO0lBQ3RGO1FBQUVKLElBQUk7UUFBV0MsTUFBTTtRQUFXQyxNQUFNO1FBQU1DLE9BQU87UUFBZ0JDLGdCQUFnQjtJQUFFO0lBQ3ZGO1FBQUVKLElBQUk7UUFBT0MsTUFBTTtRQUFPQyxNQUFNO1FBQU1DLE9BQU87UUFBaUJDLGdCQUFnQjtJQUFNO0lBQ3BGO1FBQUVKLElBQUk7UUFBU0MsTUFBTTtRQUFTQyxNQUFNO1FBQU1DLE9BQU87UUFBaUJDLGdCQUFnQjtJQUFNO0lBQ3hGO1FBQUVKLElBQUk7UUFBYUMsTUFBTTtRQUFhQyxNQUFNO1FBQU9DLE9BQU87UUFBY0MsZ0JBQWdCO0lBQU07SUFDOUY7UUFBRUosSUFBSTtRQUFPQyxNQUFNO1FBQU9DLE1BQU07UUFBTUMsT0FBTztRQUFlQyxnQkFBZ0I7SUFBTTtJQUNsRjtRQUFFSixJQUFJO1FBQVNDLE1BQU07UUFBU0MsTUFBTTtRQUFNQyxPQUFPO1FBQWlCQyxnQkFBZ0I7SUFBTTtDQUN6RjtBQUVELG9EQUFvRDtBQUM3QyxTQUFTQztJQUNkLE1BQU1DLFdBQXNCLEVBQUU7SUFDOUIsTUFBTUMsTUFBTSxJQUFJQztJQUVoQiwwQ0FBMEM7SUFDMUMsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUksS0FBS0EsSUFBSztRQUM1QixNQUFNQyxPQUFPLElBQUlGLEtBQUtEO1FBQ3RCRyxLQUFLQyxPQUFPLENBQUNELEtBQUtFLE9BQU8sS0FBS0g7UUFFOUIsMENBQTBDO1FBQzFDLE1BQU1JLGlCQUFpQkMsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUs7UUFFbEQsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlKLGdCQUFnQkksSUFBSztZQUN2QyxNQUFNQyxZQUFZbkIsY0FBYyxDQUFDZSxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS2pCLGVBQWVvQixNQUFNLEVBQUU7WUFDbkYsTUFBTUMsV0FBV04sS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssTUFBTSxFQUFFLFVBQVU7O1lBQzlELE1BQU1LLFlBQVlILFVBQVVkLGNBQWMsR0FBR2dCO1lBRTdDLDZCQUE2QjtZQUM3QixNQUFNRSxPQUFPUixLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSztZQUN4QyxNQUFNTyxTQUFTVCxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSztZQUMxQyxNQUFNUSxjQUFjLElBQUloQixLQUFLRTtZQUM3QmMsWUFBWUMsUUFBUSxDQUFDSCxNQUFNQyxRQUFRLEdBQUc7WUFFdENqQixTQUFTb0IsSUFBSSxDQUFDO2dCQUNaMUIsSUFBSSxXQUFnQmlCLE9BQUxSLEdBQUUsS0FBUUQsT0FBTFMsR0FBRSxLQUFjLE9BQVhULEtBQUtELEdBQUc7Z0JBQ2pDb0IsZUFBZVQsVUFBVWxCLEVBQUU7Z0JBQzNCNEIsZUFBZVYsVUFBVWpCLElBQUk7Z0JBQzdCNEIsZUFBZVgsVUFBVWhCLElBQUk7Z0JBQzdCNEIsZ0JBQWdCWixVQUFVZixLQUFLO2dCQUMvQmlCO2dCQUNBQyxXQUFXVSxPQUFPVixVQUFVVyxPQUFPLENBQUM7Z0JBQ3BDQyxXQUFXVDtnQkFDWFUsYUFBYWhCLFVBQVVsQixFQUFFLEtBQUssUUFBUSxXQUFXa0IsVUFBVWxCLEVBQUUsS0FBSyxjQUFjLFVBQVVtQztZQUM1RjtRQUNGO0lBQ0Y7SUFFQSxPQUFPN0IsU0FBUzhCLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxFQUFFTCxTQUFTLENBQUNNLE9BQU8sS0FBS0YsRUFBRUosU0FBUyxDQUFDTSxPQUFPO0FBQzVFO0FBRUEsaUNBQWlDO0FBQzFCLFNBQVNDLHVCQUF1QmxDLFFBQW1CLEVBQUVtQyxNQUFpRDtRQUFFL0IsT0FBQUEsaUVBQWEsSUFBSUY7SUFDOUgsTUFBTWtDLFlBQVksSUFBSWxDLEtBQUtFO0lBQzNCLE1BQU1pQyxVQUFVLElBQUluQyxLQUFLRTtJQUV6QixPQUFRK0I7UUFDTixLQUFLO1lBQ0hDLFVBQVVqQixRQUFRLENBQUMsR0FBRyxHQUFHLEdBQUc7WUFDNUJrQixRQUFRbEIsUUFBUSxDQUFDLElBQUksSUFBSSxJQUFJO1lBQzdCO1FBQ0YsS0FBSztZQUNILE1BQU1tQixZQUFZRixVQUFVRyxNQUFNO1lBQ2xDSCxVQUFVL0IsT0FBTyxDQUFDK0IsVUFBVTlCLE9BQU8sS0FBS2dDO1lBQ3hDRixVQUFVakIsUUFBUSxDQUFDLEdBQUcsR0FBRyxHQUFHO1lBQzVCa0IsUUFBUWhDLE9BQU8sQ0FBQytCLFVBQVU5QixPQUFPLEtBQUs7WUFDdEMrQixRQUFRbEIsUUFBUSxDQUFDLElBQUksSUFBSSxJQUFJO1lBQzdCO1FBQ0YsS0FBSztZQUNIaUIsVUFBVS9CLE9BQU8sQ0FBQztZQUNsQitCLFVBQVVqQixRQUFRLENBQUMsR0FBRyxHQUFHLEdBQUc7WUFDNUJrQixRQUFRRyxRQUFRLENBQUNILFFBQVFJLFFBQVEsS0FBSyxHQUFHO1lBQ3pDSixRQUFRbEIsUUFBUSxDQUFDLElBQUksSUFBSSxJQUFJO1lBQzdCO1FBQ0YsS0FBSztZQUNIaUIsVUFBVUksUUFBUSxDQUFDLEdBQUc7WUFDdEJKLFVBQVVqQixRQUFRLENBQUMsR0FBRyxHQUFHLEdBQUc7WUFDNUJrQixRQUFRRyxRQUFRLENBQUMsSUFBSTtZQUNyQkgsUUFBUWxCLFFBQVEsQ0FBQyxJQUFJLElBQUksSUFBSTtZQUM3QjtJQUNKO0lBRUEsT0FBT25CLFNBQVMwQyxNQUFNLENBQUNDLENBQUFBLFVBQ3JCQSxRQUFRaEIsU0FBUyxJQUFJUyxhQUFhTyxRQUFRaEIsU0FBUyxJQUFJVTtBQUUzRDtBQUVBLCtCQUErQjtBQUN4QixTQUFTTyxzQkFBc0I1QyxRQUFtQjtJQUN2RCxNQUFNNkMsZ0JBQWdCN0MsU0FBU2EsTUFBTTtJQUNyQyxNQUFNaUMsZ0JBQWdCOUMsU0FBUytDLE1BQU0sQ0FBQyxDQUFDQyxLQUFLTCxVQUFZSyxNQUFNTCxRQUFRN0IsUUFBUSxFQUFFO0lBQ2hGLE1BQU1tQyxpQkFBaUJqRCxTQUFTK0MsTUFBTSxDQUFDLENBQUNDLEtBQUtMLFVBQVlLLE1BQU1MLFFBQVE1QixTQUFTLEVBQUU7SUFDbEYsTUFBTW1DLG1CQUFtQkwsZ0JBQWdCLElBQUlJLGlCQUFpQkosZ0JBQWdCO0lBRTlFLE9BQU87UUFDTEE7UUFDQUM7UUFDQUcsZ0JBQWdCeEIsT0FBT3dCLGVBQWV2QixPQUFPLENBQUM7UUFDOUN3QixrQkFBa0J6QixPQUFPeUIsaUJBQWlCeEIsT0FBTyxDQUFDO0lBQ3BEO0FBQ0Y7QUFFQSwwQkFBMEI7QUFDbkIsU0FBU3lCLG9CQUFvQi9DLElBQVUsRUFBRStCLE1BQWlEO0lBQy9GLE9BQVFBO1FBQ04sS0FBSztZQUNILE9BQU8vQixLQUFLZ0Qsa0JBQWtCLENBQUMsU0FBUztnQkFDdENDLFNBQVM7Z0JBQ1RDLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLEtBQUs7WUFDUDtRQUNGLEtBQUs7WUFDSCxNQUFNQyxZQUFZLElBQUl2RCxLQUFLRTtZQUMzQixNQUFNa0MsWUFBWW1CLFVBQVVsQixNQUFNO1lBQ2xDa0IsVUFBVXBELE9BQU8sQ0FBQ29ELFVBQVVuRCxPQUFPLEtBQUtnQztZQUN4QyxNQUFNb0IsVUFBVSxJQUFJeEQsS0FBS3VEO1lBQ3pCQyxRQUFRckQsT0FBTyxDQUFDcUQsUUFBUXBELE9BQU8sS0FBSztZQUNwQyxPQUFPLEdBQWtGb0QsT0FBL0VELFVBQVVMLGtCQUFrQixDQUFDLFNBQVM7Z0JBQUVHLE9BQU87Z0JBQVNDLEtBQUs7WUFBVSxJQUFHLE9BQThGLE9BQXpGRSxRQUFRTixrQkFBa0IsQ0FBQyxTQUFTO2dCQUFFRyxPQUFPO2dCQUFTQyxLQUFLO2dCQUFXRixNQUFNO1lBQVU7UUFDakwsS0FBSztZQUNILE9BQU9sRCxLQUFLZ0Qsa0JBQWtCLENBQUMsU0FBUztnQkFBRUUsTUFBTTtnQkFBV0MsT0FBTztZQUFPO1FBQzNFLEtBQUs7WUFDSCxPQUFPbkQsS0FBS3VELFdBQVcsR0FBR0MsUUFBUTtJQUN0QztBQUNGIiwic291cmNlcyI6WyJDOlxcY2FyYm9udHJhY2tlclxcbGliXFxtb2NrLWpvdXJuZXlzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEpvdXJuZXksIFRyYW5zcG9ydE1vZGUgfSBmcm9tICdAL3R5cGVzL2pvdXJuZXknXG5cbi8vIFNpbXBsZSBzZWVkZWQgcmFuZG9tIG51bWJlciBnZW5lcmF0b3IgZm9yIGNvbnNpc3RlbnQgcmVzdWx0c1xuY2xhc3MgU2VlZGVkUmFuZG9tIHtcbiAgcHJpdmF0ZSBzZWVkOiBudW1iZXJcblxuICBjb25zdHJ1Y3RvcihzZWVkOiBudW1iZXIpIHtcbiAgICB0aGlzLnNlZWQgPSBzZWVkXG4gIH1cblxuICBuZXh0KCk6IG51bWJlciB7XG4gICAgdGhpcy5zZWVkID0gKHRoaXMuc2VlZCAqIDkzMDEgKyA0OTI5NykgJSAyMzMyODBcbiAgICByZXR1cm4gdGhpcy5zZWVkIC8gMjMzMjgwXG4gIH1cbn1cblxuZXhwb3J0IGNvbnN0IHRyYW5zcG9ydE1vZGVzOiBUcmFuc3BvcnRNb2RlW10gPSBbXG4gIHsgaWQ6ICdiaWN5Y2xlJywgbmFtZTogJ0JpY3ljbGUnLCBpY29uOiAn8J+asicsIGNvbG9yOiAnYmctYmx1ZS00MDAnLCBlbWlzc2lvbkZhY3RvcjogMCB9LFxuICB7IGlkOiAnd2Fsa2luZycsIG5hbWU6ICdXYWxraW5nJywgaWNvbjogJ/CfmrYnLCBjb2xvcjogJ2JnLWdyZWVuLTQwMCcsIGVtaXNzaW9uRmFjdG9yOiAwIH0sXG4gIHsgaWQ6ICdidXMnLCBuYW1lOiAnQnVzJywgaWNvbjogJ/CfmownLCBjb2xvcjogJ2JnLXllbGxvdy00MDAnLCBlbWlzc2lvbkZhY3RvcjogMC4wODkgfSxcbiAgeyBpZDogJ3RyYWluJywgbmFtZTogJ1RyYWluJywgaWNvbjogJ/CfmoonLCBjb2xvcjogJ2JnLW9yYW5nZS00MDAnLCBlbWlzc2lvbkZhY3RvcjogMC4wNDEgfSxcbiAgeyBpZDogJ21vdG9yYmlrZScsIG5hbWU6ICdNb3RvcmJpa2UnLCBpY29uOiAn8J+Pje+4jycsIGNvbG9yOiAnYmctcmVkLTQwMCcsIGVtaXNzaW9uRmFjdG9yOiAwLjExMyB9LFxuICB7IGlkOiAnY2FyJywgbmFtZTogJ0NhcicsIGljb246ICfwn5qXJywgY29sb3I6ICdiZy1ncmF5LTQwMCcsIGVtaXNzaW9uRmFjdG9yOiAwLjE3MSB9LFxuICB7IGlkOiAncGxhbmUnLCBuYW1lOiAnUGxhbmUnLCBpY29uOiAn4pyI77iPJywgY29sb3I6ICdiZy1wdXJwbGUtNDAwJywgZW1pc3Npb25GYWN0b3I6IDAuMjU1IH0sXG5dXG5cbi8vIEdlbmVyYXRlIG1vY2sgam91cm5leXMgZm9yIGRpZmZlcmVudCB0aW1lIHBlcmlvZHNcbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZU1vY2tKb3VybmV5cygpOiBKb3VybmV5W10ge1xuICBjb25zdCBqb3VybmV5czogSm91cm5leVtdID0gW11cbiAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxuICBcbiAgLy8gR2VuZXJhdGUgam91cm5leXMgZm9yIHRoZSBwYXN0IDM2NSBkYXlzXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgMzY1OyBpKyspIHtcbiAgICBjb25zdCBkYXRlID0gbmV3IERhdGUobm93KVxuICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSAtIGkpXG4gICAgXG4gICAgLy8gUmFuZG9tIG51bWJlciBvZiBqb3VybmV5cyBwZXIgZGF5ICgwLTQpXG4gICAgY29uc3Qgam91cm5leXNQZXJEYXkgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiA1KVxuICAgIFxuICAgIGZvciAobGV0IGogPSAwOyBqIDwgam91cm5leXNQZXJEYXk7IGorKykge1xuICAgICAgY29uc3QgdHJhbnNwb3J0ID0gdHJhbnNwb3J0TW9kZXNbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogdHJhbnNwb3J0TW9kZXMubGVuZ3RoKV1cbiAgICAgIGNvbnN0IGRpc3RhbmNlID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogNTApICsgMSAvLyAxLTUwIGttXG4gICAgICBjb25zdCBlbWlzc2lvbnMgPSB0cmFuc3BvcnQuZW1pc3Npb25GYWN0b3IgKiBkaXN0YW5jZVxuICAgICAgXG4gICAgICAvLyBSYW5kb20gdGltZSBkdXJpbmcgdGhlIGRheVxuICAgICAgY29uc3QgaG91ciA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDI0KVxuICAgICAgY29uc3QgbWludXRlID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogNjApXG4gICAgICBjb25zdCBqb3VybmV5VGltZSA9IG5ldyBEYXRlKGRhdGUpXG4gICAgICBqb3VybmV5VGltZS5zZXRIb3Vycyhob3VyLCBtaW51dGUsIDAsIDApXG4gICAgICBcbiAgICAgIGpvdXJuZXlzLnB1c2goe1xuICAgICAgICBpZDogYGpvdXJuZXktJHtpfS0ke2p9LSR7RGF0ZS5ub3coKX1gLFxuICAgICAgICB0cmFuc3BvcnRNb2RlOiB0cmFuc3BvcnQuaWQsXG4gICAgICAgIHRyYW5zcG9ydE5hbWU6IHRyYW5zcG9ydC5uYW1lLFxuICAgICAgICB0cmFuc3BvcnRJY29uOiB0cmFuc3BvcnQuaWNvbixcbiAgICAgICAgdHJhbnNwb3J0Q29sb3I6IHRyYW5zcG9ydC5jb2xvcixcbiAgICAgICAgZGlzdGFuY2UsXG4gICAgICAgIGVtaXNzaW9uczogTnVtYmVyKGVtaXNzaW9ucy50b0ZpeGVkKDIpKSxcbiAgICAgICAgdGltZXN0YW1wOiBqb3VybmV5VGltZSxcbiAgICAgICAgdmVoaWNsZVR5cGU6IHRyYW5zcG9ydC5pZCA9PT0gJ2NhcicgPyAnbWVkaXVtJyA6IHRyYW5zcG9ydC5pZCA9PT0gJ21vdG9yYmlrZScgPyAnMjUwY2MnIDogdW5kZWZpbmVkXG4gICAgICB9KVxuICAgIH1cbiAgfVxuICBcbiAgcmV0dXJuIGpvdXJuZXlzLnNvcnQoKGEsIGIpID0+IGIudGltZXN0YW1wLmdldFRpbWUoKSAtIGEudGltZXN0YW1wLmdldFRpbWUoKSlcbn1cblxuLy8gRmlsdGVyIGpvdXJuZXlzIGJ5IHRpbWUgcGVyaW9kXG5leHBvcnQgZnVuY3Rpb24gZmlsdGVySm91cm5leXNCeVBlcmlvZChqb3VybmV5czogSm91cm5leVtdLCBwZXJpb2Q6ICdkYWlseScgfCAnd2Vla2x5JyB8ICdtb250aGx5JyB8ICd5ZWFybHknLCBkYXRlOiBEYXRlID0gbmV3IERhdGUoKSk6IEpvdXJuZXlbXSB7XG4gIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKGRhdGUpXG4gIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZShkYXRlKVxuICBcbiAgc3dpdGNoIChwZXJpb2QpIHtcbiAgICBjYXNlICdkYWlseSc6XG4gICAgICBzdGFydERhdGUuc2V0SG91cnMoMCwgMCwgMCwgMClcbiAgICAgIGVuZERhdGUuc2V0SG91cnMoMjMsIDU5LCA1OSwgOTk5KVxuICAgICAgYnJlYWtcbiAgICBjYXNlICd3ZWVrbHknOlxuICAgICAgY29uc3QgZGF5T2ZXZWVrID0gc3RhcnREYXRlLmdldERheSgpXG4gICAgICBzdGFydERhdGUuc2V0RGF0ZShzdGFydERhdGUuZ2V0RGF0ZSgpIC0gZGF5T2ZXZWVrKVxuICAgICAgc3RhcnREYXRlLnNldEhvdXJzKDAsIDAsIDAsIDApXG4gICAgICBlbmREYXRlLnNldERhdGUoc3RhcnREYXRlLmdldERhdGUoKSArIDYpXG4gICAgICBlbmREYXRlLnNldEhvdXJzKDIzLCA1OSwgNTksIDk5OSlcbiAgICAgIGJyZWFrXG4gICAgY2FzZSAnbW9udGhseSc6XG4gICAgICBzdGFydERhdGUuc2V0RGF0ZSgxKVxuICAgICAgc3RhcnREYXRlLnNldEhvdXJzKDAsIDAsIDAsIDApXG4gICAgICBlbmREYXRlLnNldE1vbnRoKGVuZERhdGUuZ2V0TW9udGgoKSArIDEsIDApXG4gICAgICBlbmREYXRlLnNldEhvdXJzKDIzLCA1OSwgNTksIDk5OSlcbiAgICAgIGJyZWFrXG4gICAgY2FzZSAneWVhcmx5JzpcbiAgICAgIHN0YXJ0RGF0ZS5zZXRNb250aCgwLCAxKVxuICAgICAgc3RhcnREYXRlLnNldEhvdXJzKDAsIDAsIDAsIDApXG4gICAgICBlbmREYXRlLnNldE1vbnRoKDExLCAzMSlcbiAgICAgIGVuZERhdGUuc2V0SG91cnMoMjMsIDU5LCA1OSwgOTk5KVxuICAgICAgYnJlYWtcbiAgfVxuICBcbiAgcmV0dXJuIGpvdXJuZXlzLmZpbHRlcihqb3VybmV5ID0+IFxuICAgIGpvdXJuZXkudGltZXN0YW1wID49IHN0YXJ0RGF0ZSAmJiBqb3VybmV5LnRpbWVzdGFtcCA8PSBlbmREYXRlXG4gIClcbn1cblxuLy8gQ2FsY3VsYXRlIGpvdXJuZXkgc3RhdGlzdGljc1xuZXhwb3J0IGZ1bmN0aW9uIGNhbGN1bGF0ZUpvdXJuZXlTdGF0cyhqb3VybmV5czogSm91cm5leVtdKSB7XG4gIGNvbnN0IHRvdGFsSm91cm5leXMgPSBqb3VybmV5cy5sZW5ndGhcbiAgY29uc3QgdG90YWxEaXN0YW5jZSA9IGpvdXJuZXlzLnJlZHVjZSgoc3VtLCBqb3VybmV5KSA9PiBzdW0gKyBqb3VybmV5LmRpc3RhbmNlLCAwKVxuICBjb25zdCB0b3RhbEVtaXNzaW9ucyA9IGpvdXJuZXlzLnJlZHVjZSgoc3VtLCBqb3VybmV5KSA9PiBzdW0gKyBqb3VybmV5LmVtaXNzaW9ucywgMClcbiAgY29uc3QgYXZlcmFnZUVtaXNzaW9ucyA9IHRvdGFsSm91cm5leXMgPiAwID8gdG90YWxFbWlzc2lvbnMgLyB0b3RhbEpvdXJuZXlzIDogMFxuICBcbiAgcmV0dXJuIHtcbiAgICB0b3RhbEpvdXJuZXlzLFxuICAgIHRvdGFsRGlzdGFuY2UsXG4gICAgdG90YWxFbWlzc2lvbnM6IE51bWJlcih0b3RhbEVtaXNzaW9ucy50b0ZpeGVkKDIpKSxcbiAgICBhdmVyYWdlRW1pc3Npb25zOiBOdW1iZXIoYXZlcmFnZUVtaXNzaW9ucy50b0ZpeGVkKDIpKVxuICB9XG59XG5cbi8vIEZvcm1hdCBkYXRlIGZvciBkaXNwbGF5XG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0RGF0ZUZvclBlcmlvZChkYXRlOiBEYXRlLCBwZXJpb2Q6ICdkYWlseScgfCAnd2Vla2x5JyB8ICdtb250aGx5JyB8ICd5ZWFybHknKTogc3RyaW5nIHtcbiAgc3dpdGNoIChwZXJpb2QpIHtcbiAgICBjYXNlICdkYWlseSc6XG4gICAgICByZXR1cm4gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywgeyBcbiAgICAgICAgd2Vla2RheTogJ2xvbmcnLCBcbiAgICAgICAgeWVhcjogJ251bWVyaWMnLCBcbiAgICAgICAgbW9udGg6ICdsb25nJywgXG4gICAgICAgIGRheTogJ251bWVyaWMnIFxuICAgICAgfSlcbiAgICBjYXNlICd3ZWVrbHknOlxuICAgICAgY29uc3Qgd2Vla1N0YXJ0ID0gbmV3IERhdGUoZGF0ZSlcbiAgICAgIGNvbnN0IGRheU9mV2VlayA9IHdlZWtTdGFydC5nZXREYXkoKVxuICAgICAgd2Vla1N0YXJ0LnNldERhdGUod2Vla1N0YXJ0LmdldERhdGUoKSAtIGRheU9mV2VlaylcbiAgICAgIGNvbnN0IHdlZWtFbmQgPSBuZXcgRGF0ZSh3ZWVrU3RhcnQpXG4gICAgICB3ZWVrRW5kLnNldERhdGUod2Vla0VuZC5nZXREYXRlKCkgKyA2KVxuICAgICAgcmV0dXJuIGAke3dlZWtTdGFydC50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywgeyBtb250aDogJ3Nob3J0JywgZGF5OiAnbnVtZXJpYycgfSl9IC0gJHt3ZWVrRW5kLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCB7IG1vbnRoOiAnc2hvcnQnLCBkYXk6ICdudW1lcmljJywgeWVhcjogJ251bWVyaWMnIH0pfWBcbiAgICBjYXNlICdtb250aGx5JzpcbiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCB7IHllYXI6ICdudW1lcmljJywgbW9udGg6ICdsb25nJyB9KVxuICAgIGNhc2UgJ3llYXJseSc6XG4gICAgICByZXR1cm4gZGF0ZS5nZXRGdWxsWWVhcigpLnRvU3RyaW5nKClcbiAgfVxufVxuIl0sIm5hbWVzIjpbIlNlZWRlZFJhbmRvbSIsIm5leHQiLCJzZWVkIiwidHJhbnNwb3J0TW9kZXMiLCJpZCIsIm5hbWUiLCJpY29uIiwiY29sb3IiLCJlbWlzc2lvbkZhY3RvciIsImdlbmVyYXRlTW9ja0pvdXJuZXlzIiwiam91cm5leXMiLCJub3ciLCJEYXRlIiwiaSIsImRhdGUiLCJzZXREYXRlIiwiZ2V0RGF0ZSIsImpvdXJuZXlzUGVyRGF5IiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwiaiIsInRyYW5zcG9ydCIsImxlbmd0aCIsImRpc3RhbmNlIiwiZW1pc3Npb25zIiwiaG91ciIsIm1pbnV0ZSIsImpvdXJuZXlUaW1lIiwic2V0SG91cnMiLCJwdXNoIiwidHJhbnNwb3J0TW9kZSIsInRyYW5zcG9ydE5hbWUiLCJ0cmFuc3BvcnRJY29uIiwidHJhbnNwb3J0Q29sb3IiLCJOdW1iZXIiLCJ0b0ZpeGVkIiwidGltZXN0YW1wIiwidmVoaWNsZVR5cGUiLCJ1bmRlZmluZWQiLCJzb3J0IiwiYSIsImIiLCJnZXRUaW1lIiwiZmlsdGVySm91cm5leXNCeVBlcmlvZCIsInBlcmlvZCIsInN0YXJ0RGF0ZSIsImVuZERhdGUiLCJkYXlPZldlZWsiLCJnZXREYXkiLCJzZXRNb250aCIsImdldE1vbnRoIiwiZmlsdGVyIiwiam91cm5leSIsImNhbGN1bGF0ZUpvdXJuZXlTdGF0cyIsInRvdGFsSm91cm5leXMiLCJ0b3RhbERpc3RhbmNlIiwicmVkdWNlIiwic3VtIiwidG90YWxFbWlzc2lvbnMiLCJhdmVyYWdlRW1pc3Npb25zIiwiZm9ybWF0RGF0ZUZvclBlcmlvZCIsInRvTG9jYWxlRGF0ZVN0cmluZyIsIndlZWtkYXkiLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJ3ZWVrU3RhcnQiLCJ3ZWVrRW5kIiwiZ2V0RnVsbFllYXIiLCJ0b1N0cmluZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-journeys.ts\n"));

/***/ })

});