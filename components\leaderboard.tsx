"use client"

import React from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

interface LeaderboardUser {
  id: number
  name: string
  emissions: number
  avatar?: string
}

interface LeaderboardProps {
  users: LeaderboardUser[]
  className?: string
}

export function Leaderboard({ users, className = "" }: LeaderboardProps) {
  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Leaderboard</h3>
      
      {users.map((user, index) => (
        <div key={user.id} className="flex items-center justify-between p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            <div className="flex items-center justify-center w-8 h-8 bg-emerald-100 rounded-full text-sm font-bold text-emerald-700 flex-shrink-0">
              {index + 1}
            </div>
            <Avatar className="w-10 h-10 flex-shrink-0">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback className="text-sm font-medium bg-emerald-500 text-white">
                {user.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="font-medium text-gray-800 truncate" title={user.name}>
              {user.name}
            </span>
          </div>
          <span className="text-sm font-semibold text-emerald-600 flex-shrink-0 ml-2">
            {user.emissions}kg CO₂
          </span>
        </div>
      ))}
    </div>
  )
}
