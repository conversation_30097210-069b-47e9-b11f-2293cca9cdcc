"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'

interface TransportMode {
  id: string
  name: string
  icon: string
  color: string
  emissionFactor: number // kg CO2 per km
}

const transportModes: TransportMode[] = [
  { id: 'bicycle', name: 'Bicycle', icon: '🚲', color: 'bg-emerald-500', emissionFactor: 0 },
  { id: 'walking', name: 'Walking', icon: '🚶', color: 'bg-green-500', emissionFactor: 0 },
  { id: 'bus', name: 'Bus', icon: '🚌', color: 'bg-amber-500', emissionFactor: 0.089 },
  { id: 'train', name: 'Train', icon: '🚊', color: 'bg-blue-500', emissionFactor: 0.041 },
  { id: 'motorbike', name: 'Motorbike', icon: '🏍️', color: 'bg-orange-500', emissionFactor: 0.113 },
  { id: 'car', name: 'Car', icon: '🚗', color: 'bg-slate-500', emissionFactor: 0.171 },
  { id: 'plane', name: 'Plane', icon: '✈️', color: 'bg-red-500', emissionFactor: 0.255 },
]

interface JourneyFormProps {
  onSubmit?: (data: { transport: string; mileage: number; type: string; emissions: number }) => void
  className?: string
}

export function JourneyForm({ onSubmit, className = "" }: JourneyFormProps) {
  const [selectedTransport, setSelectedTransport] = useState<string>('car')
  const [mileage, setMileage] = useState<number[]>([20])
  const [vehicleType, setVehicleType] = useState<string>('125cc')

  const selectedMode = transportModes.find(mode => mode.id === selectedTransport)
  const estimatedEmissions = selectedMode ? (selectedMode.emissionFactor * mileage[0]).toFixed(2) : '0'

  const handleSubmit = () => {
    const emissions = selectedMode ? selectedMode.emissionFactor * mileage[0] : 0
    onSubmit?.({
      transport: selectedTransport,
      mileage: mileage[0],
      type: vehicleType,
      emissions
    })
  }

  return (
    <div className={`space-y-6 p-2 sm:p-0 ${className}`}>
      {/* Header */}
      <DialogHeader className="text-center pb-4 border-b border-gray-100">
        <DialogTitle className="text-2xl font-bold text-gray-800 mb-2">Add Journey</DialogTitle>
        <DialogDescription className="text-gray-600 text-base">
          Let&apos;s track your carbon footprint and make a positive impact! 🌱
        </DialogDescription>
      </DialogHeader>

      {/* Transport Mode Selection */}
      <div className="space-y-4">
        <label className="text-base font-semibold text-gray-800">Transport Mode</label>
        <Select value={selectedTransport} onValueChange={setSelectedTransport}>
          <SelectTrigger className="w-full h-14 rounded-xl border-2 border-gray-200 bg-white/80 backdrop-blur-sm">
            <SelectValue>
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full ${selectedMode?.color} flex items-center justify-center text-white text-lg`}>
                  {selectedMode?.icon}
                </div>
                <span className="font-semibold text-gray-800">{selectedMode?.name}</span>
              </div>
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="rounded-xl border-2 border-gray-200">
            {transportModes.map((mode) => (
              <SelectItem key={mode.id} value={mode.id} className="py-3">
                <div className="flex items-center space-x-3 w-full">
                  <div className={`w-8 h-8 rounded-full ${mode.color} flex items-center justify-center text-white text-sm`}>
                    {mode.icon}
                  </div>
                  <div className="flex-1">
                    <span className="font-medium">{mode.name}</span>
                    <div className="text-xs text-gray-500">
                      {mode.emissionFactor === 0 ? 'Zero emissions' : `${mode.emissionFactor} kg CO₂/km`}
                    </div>
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Distance Slider */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <label className="text-base font-semibold text-gray-800">Distance</label>
          <span className="text-lg font-bold text-emerald-600 bg-emerald-50 px-3 py-1 rounded-lg">{mileage[0]} km</span>
        </div>
        <div className="bg-gray-50 rounded-xl p-4">
          <Slider
            value={mileage}
            onValueChange={setMileage}
            max={200}
            min={1}
            step={1}
            className="w-full"
          />
          <div className="flex justify-between text-sm text-gray-500 mt-2">
            <span>1 km</span>
            <span>200 km</span>
          </div>
        </div>
      </div>

      {/* Vehicle Type - Only show for motorized transport */}
      {(selectedTransport === 'car' || selectedTransport === 'motorbike') && (
        <div className="space-y-4">
          <label className="text-base font-semibold text-gray-800">
            {selectedTransport === 'car' ? 'Car Type' : 'Engine Size'}
          </label>
          <Select value={vehicleType} onValueChange={setVehicleType}>
            <SelectTrigger className="h-12 rounded-xl border-2 border-gray-200 bg-white/80 backdrop-blur-sm">
              <SelectValue placeholder={`Select ${selectedTransport} type`} />
            </SelectTrigger>
            <SelectContent className="rounded-xl border-2 border-gray-200">
              {selectedTransport === 'car' ? (
                <>
                  <SelectItem value="small" className="py-2">Small Car</SelectItem>
                  <SelectItem value="medium" className="py-2">Medium Car</SelectItem>
                  <SelectItem value="large" className="py-2">Large Car</SelectItem>
                  <SelectItem value="suv" className="py-2">SUV</SelectItem>
                  <SelectItem value="electric" className="py-2">Electric Car</SelectItem>
                </>
              ) : (
                <>
                  <SelectItem value="125cc" className="py-2">Up to 125cc</SelectItem>
                  <SelectItem value="250cc" className="py-2">Up to 250cc</SelectItem>
                  <SelectItem value="500cc" className="py-2">Up to 500cc</SelectItem>
                  <SelectItem value="750cc" className="py-2">Up to 750cc</SelectItem>
                  <SelectItem value="1000cc" className="py-2">Over 1000cc</SelectItem>
                </>
              )}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Emissions Preview */}
      <div className="bg-gradient-to-r from-emerald-50 to-green-50 border-2 border-emerald-200 rounded-2xl p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-base font-semibold text-emerald-800">Estimated Emissions</p>
            <p className="text-sm text-emerald-600">Based on your journey details</p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-emerald-700">{estimatedEmissions} kg</p>
            <p className="text-sm text-emerald-600">CO₂</p>
          </div>
        </div>
      </div>

      {/* Calculate Button */}
      <Button
        onClick={handleSubmit}
        className="w-full bg-gradient-to-r from-emerald-500 to-green-600 text-white font-bold py-4 rounded-2xl text-lg shadow-lg"
      >
        Add Journey
      </Button>
    </div>
  )
}
