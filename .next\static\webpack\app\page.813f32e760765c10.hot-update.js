"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/mock-journeys.ts":
/*!******************************!*\
  !*** ./lib/mock-journeys.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateJourneyStats: () => (/* binding */ calculateJourneyStats),\n/* harmony export */   filterJourneysByPeriod: () => (/* binding */ filterJourneysByPeriod),\n/* harmony export */   formatDateForPeriod: () => (/* binding */ formatDateForPeriod),\n/* harmony export */   generateMockJourneys: () => (/* binding */ generateMockJourneys),\n/* harmony export */   transportModes: () => (/* binding */ transportModes)\n/* harmony export */ });\n// Simple seeded random number generator for consistent results\nclass SeededRandom {\n    next() {\n        this.seed = (this.seed * 9301 + 49297) % 233280;\n        return this.seed / 233280;\n    }\n    constructor(seed){\n        this.seed = seed;\n    }\n}\nconst transportModes = [\n    {\n        id: 'bicycle',\n        name: 'Bicycle',\n        icon: '🚲',\n        color: 'bg-emerald-500',\n        emissionFactor: 0\n    },\n    {\n        id: 'walking',\n        name: 'Walking',\n        icon: '🚶',\n        color: 'bg-green-500',\n        emissionFactor: 0\n    },\n    {\n        id: 'bus',\n        name: 'Bus',\n        icon: '🚌',\n        color: 'bg-amber-500',\n        emissionFactor: 0.089\n    },\n    {\n        id: 'train',\n        name: 'Train',\n        icon: '🚊',\n        color: 'bg-blue-500',\n        emissionFactor: 0.041\n    },\n    {\n        id: 'motorbike',\n        name: 'Motorbike',\n        icon: '🏍️',\n        color: 'bg-orange-500',\n        emissionFactor: 0.113\n    },\n    {\n        id: 'car',\n        name: 'Car',\n        icon: '🚗',\n        color: 'bg-slate-500',\n        emissionFactor: 0.171\n    },\n    {\n        id: 'plane',\n        name: 'Plane',\n        icon: '✈️',\n        color: 'bg-red-500',\n        emissionFactor: 0.255\n    }\n];\n// Generate mock journeys for different time periods\nfunction generateMockJourneys() {\n    const journeys = [];\n    const rng = new SeededRandom(12345) // Fixed seed for consistent results\n    ;\n    const baseDate = new Date('2024-01-01') // Fixed base date\n    ;\n    const now = new Date();\n    // Generate journeys for the past 365 days from today\n    for(let i = 0; i < 365; i++){\n        const date = new Date(now);\n        date.setDate(date.getDate() - i);\n        // Seeded random number of journeys per day (0-4)\n        const journeysPerDay = Math.floor(rng.next() * 5);\n        for(let j = 0; j < journeysPerDay; j++){\n            const transport = transportModes[Math.floor(rng.next() * transportModes.length)];\n            const distance = Math.floor(rng.next() * 50) + 1 // 1-50 km\n            ;\n            const emissions = transport.emissionFactor * distance;\n            // Seeded random time during the day\n            const hour = Math.floor(rng.next() * 24);\n            const minute = Math.floor(rng.next() * 60);\n            const journeyTime = new Date(date);\n            journeyTime.setHours(hour, minute, 0, 0);\n            journeys.push({\n                id: \"journey-\".concat(i, \"-\").concat(j),\n                transportMode: transport.id,\n                transportName: transport.name,\n                transportIcon: transport.icon,\n                transportColor: transport.color,\n                distance,\n                emissions: emissions === 0 ? 0 : Number(emissions.toFixed(1)),\n                timestamp: journeyTime,\n                vehicleType: transport.id === 'car' ? 'medium' : transport.id === 'motorbike' ? '250cc' : undefined\n            });\n        }\n    }\n    return journeys.sort((a, b)=>b.timestamp.getTime() - a.timestamp.getTime());\n}\n// Filter journeys by time period\nfunction filterJourneysByPeriod(journeys, period) {\n    let date = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : new Date();\n    const startDate = new Date(date);\n    const endDate = new Date(date);\n    switch(period){\n        case 'daily':\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'weekly':\n            const dayOfWeek = startDate.getDay();\n            startDate.setDate(startDate.getDate() - dayOfWeek);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setDate(startDate.getDate() + 6);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'monthly':\n            startDate.setDate(1);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setMonth(endDate.getMonth() + 1, 0);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'yearly':\n            startDate.setMonth(0, 1);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setMonth(11, 31);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n    }\n    return journeys.filter((journey)=>journey.timestamp >= startDate && journey.timestamp <= endDate);\n}\n// Calculate journey statistics\nfunction calculateJourneyStats(journeys) {\n    const totalJourneys = journeys.length;\n    const totalDistance = journeys.reduce((sum, journey)=>sum + journey.distance, 0);\n    const totalEmissions = journeys.reduce((sum, journey)=>sum + journey.emissions, 0);\n    const averageEmissions = totalJourneys > 0 ? totalEmissions / totalJourneys : 0;\n    return {\n        totalJourneys,\n        totalDistance,\n        totalEmissions: Number(totalEmissions.toFixed(2)),\n        averageEmissions: Number(averageEmissions.toFixed(2))\n    };\n}\n// Format date for display\nfunction formatDateForPeriod(date, period) {\n    switch(period){\n        case 'daily':\n            return date.toLocaleDateString('en-US', {\n                weekday: 'long',\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n            });\n        case 'weekly':\n            const weekStart = new Date(date);\n            const dayOfWeek = weekStart.getDay();\n            weekStart.setDate(weekStart.getDate() - dayOfWeek);\n            const weekEnd = new Date(weekStart);\n            weekEnd.setDate(weekEnd.getDate() + 6);\n            return \"\".concat(weekStart.toLocaleDateString('en-US', {\n                month: 'short',\n                day: 'numeric'\n            }), \" - \").concat(weekEnd.toLocaleDateString('en-US', {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric'\n            }));\n        case 'monthly':\n            return date.toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long'\n            });\n        case 'yearly':\n            return date.getFullYear().toString();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-journeys.ts\n"));

/***/ })

});