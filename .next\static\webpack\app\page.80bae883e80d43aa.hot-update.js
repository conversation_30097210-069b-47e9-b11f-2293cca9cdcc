"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_modern_gauge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/modern-gauge */ \"(app-pages-browser)/./components/modern-gauge.tsx\");\n/* harmony import */ var _components_shadcn_line_chart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shadcn-line-chart */ \"(app-pages-browser)/./components/shadcn-line-chart.tsx\");\n/* harmony import */ var _components_leaderboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/leaderboard */ \"(app-pages-browser)/./components/leaderboard.tsx\");\n/* harmony import */ var _components_journey_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/journey-form */ \"(app-pages-browser)/./components/journey-form.tsx\");\n/* harmony import */ var _components_journey_history__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/journey-history */ \"(app-pages-browser)/./components/journey-history.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_FiPlus_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FiPlus!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_mock_journeys__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/mock-journeys */ \"(app-pages-browser)/./lib/mock-journeys.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [currentEmissions, setCurrentEmissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(120);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [journeys, setJourneys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Home.useState\": ()=>(0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_10__.generateMockJourneys)()\n    }[\"Home.useState\"]);\n    // Sample data\n    const consumptionData = [\n        {\n            month: 'Jan',\n            value: 520\n        },\n        {\n            month: 'Feb',\n            value: 480\n        },\n        {\n            month: 'Mar',\n            value: 620\n        },\n        {\n            month: 'Apr',\n            value: 380\n        },\n        {\n            month: 'May',\n            value: 450\n        },\n        {\n            month: 'Jun',\n            value: 590\n        },\n        {\n            month: 'Jul',\n            value: 680\n        },\n        {\n            month: 'Aug',\n            value: 520\n        },\n        {\n            month: 'Sep',\n            value: 380\n        },\n        {\n            month: 'Oct',\n            value: 680\n        },\n        {\n            month: 'Nov',\n            value: 590\n        },\n        {\n            month: 'Dec',\n            value: 740\n        }\n    ];\n    const leaderboardData = [\n        {\n            id: 1,\n            name: 'Anita',\n            emissions: 450,\n            avatar: '/avatars/anita.jpg'\n        },\n        {\n            id: 2,\n            name: 'Diana',\n            emissions: 464,\n            avatar: '/avatars/diana.jpg'\n        },\n        {\n            id: 3,\n            name: 'Lucia',\n            emissions: 532,\n            avatar: '/avatars/lucia.jpg'\n        },\n        {\n            id: 4,\n            name: 'Oliver Deak',\n            emissions: 740,\n            avatar: '/avatars/oliver.jpg'\n        }\n    ];\n    const handleJourneySubmit = (data)=>{\n        const transportMode = _lib_mock_journeys__WEBPACK_IMPORTED_MODULE_10__.transportModes.find((mode)=>mode.id === data.transport);\n        if (transportMode) {\n            const newJourney = {\n                id: \"journey-new-\".concat(journeys.length),\n                transportMode: data.transport,\n                transportName: transportMode.name,\n                transportIcon: transportMode.icon,\n                transportColor: transportMode.color,\n                distance: data.mileage,\n                emissions: data.emissions === 0 ? 0 : Number(data.emissions.toFixed(1)),\n                timestamp: new Date(),\n                vehicleType: data.type\n            };\n            setJourneys((prev)=>[\n                    newJourney,\n                    ...prev\n                ]);\n            setCurrentEmissions((prev)=>prev + data.emissions);\n        }\n        setIsModalOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-mesh-gradient\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden p-4 sm:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-sm sm:max-w-md mx-auto space-y-4 sm:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl p-4 sm:p-6 text-white shadow-xl shadow-emerald-500/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4 sm:mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl sm:text-2xl font-bold\",\n                                                    children: \"Hello, Sarah\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-emerald-100 text-xs sm:text-sm\",\n                                                    children: \"Your emission is going well \\uD83C\\uDF31\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                            className: \"w-12 h-12 sm:w-14 sm:h-14 border-2 sm:border-3 border-white/30 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarImage, {\n                                                    src: \"/avatars/sarah.jpg\",\n                                                    alt: \"Sarah\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarFallback, {\n                                                    className: \"bg-emerald-600 text-white font-semibold text-sm sm:text-base\",\n                                                    children: \"S\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-4 sm:mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modern_gauge__WEBPACK_IMPORTED_MODULE_2__.ModernGauge, {\n                                        value: currentEmissions,\n                                        max: 200,\n                                        size: 160\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl sm:text-2xl font-bold text-white\",\n                                                    children: [\n                                                        Math.round(currentEmissions / 200 * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-emerald-100\",\n                                                    children: \"Monthly Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl sm:text-2xl font-bold text-white\",\n                                                    children: 200 - currentEmissions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-emerald-100\",\n                                                    children: \"kg Remaining\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                                    open: isModalOpen,\n                                    onOpenChange: setIsModalOpen,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                className: \"w-full glass text-white font-semibold py-3 sm:py-4 rounded-xl sm:rounded-2xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiPlus_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiPlus, {\n                                                        className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Add Journey\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                            className: \"sm:max-w-lg mx-4 max-h-[90vh] overflow-y-auto rounded-3xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_form__WEBPACK_IMPORTED_MODULE_5__.JourneyForm, {\n                                                onSubmit: handleJourneySubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-glass\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_line_chart__WEBPACK_IMPORTED_MODULE_3__.ShadcnLineChart, {\n                                data: consumptionData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_history__WEBPACK_IMPORTED_MODULE_6__.JourneyHistory, {\n                            journeys: journeys\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-glass p-4 sm:p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leaderboard__WEBPACK_IMPORTED_MODULE_4__.Leaderboard, {\n                                users: leaderboardData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block p-6 xl:p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-glass p-8 relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-emerald-500/10 to-green-600/10 rounded-full blur-3xl -translate-y-32 translate-x-32\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-6 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                                                className: \"w-16 h-16 border-3 border-emerald-200 shadow-glow-emerald\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarImage, {\n                                                                        src: \"/avatars/sarah.jpg\",\n                                                                        alt: \"Sarah\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 144,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarFallback, {\n                                                                        className: \"bg-emerald-500 text-white font-bold text-lg\",\n                                                                        children: \"S\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 145,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                        className: \"text-3xl xl:text-4xl font-bold text-gray-800 mb-1\",\n                                                                        children: \"Hello, Sarah\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 148,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 text-lg\",\n                                                                        children: \"Your emission tracking is going well \\uD83C\\uDF31\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 149,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 bg-emerald-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 156,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"Current: \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold text-gray-800\",\n                                                                                children: [\n                                                                                    currentEmissions,\n                                                                                    \"kg CO₂\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 157,\n                                                                                columnNumber: 72\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 157,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 bg-amber-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 160,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"Target: \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold text-gray-800\",\n                                                                                children: \"200kg CO₂\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 161,\n                                                                                columnNumber: 71\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 161,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                                                    open: isModalOpen,\n                                                    onOpenChange: setIsModalOpen,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                                className: \"bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold px-8 py-4 rounded-2xl text-lg shadow-glow-emerald transition-all duration-300 hover:scale-105\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiPlus_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiPlus, {\n                                                                        className: \"w-5 h-5 mr-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Add Journey\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                                            className: \"sm:max-w-lg max-h-[90vh] overflow-y-auto rounded-3xl border-2 border-gray-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_form__WEBPACK_IMPORTED_MODULE_5__.JourneyForm, {\n                                                                onSubmit: handleJourneySubmit\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-12 gap-6 xl:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-5 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl p-6 xl:p-8 text-white shadow-2xl shadow-emerald-500/25 relative overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-20 -right-20 w-40 h-40 bg-white/10 rounded-full blur-2xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -bottom-10 -left-10 w-32 h-32 bg-white/5 rounded-full blur-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-xl xl:text-2xl font-bold mb-1\",\n                                                                    children: \"Monthly Progress\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-emerald-100 text-sm\",\n                                                                    children: \"Track your carbon footprint\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-center mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modern_gauge__WEBPACK_IMPORTED_MODULE_2__.ModernGauge, {\n                                                                value: currentEmissions,\n                                                                max: 200,\n                                                                size: 220\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card-glass p-6 relative overflow-hidden group hover:shadow-lg transition-all duration-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-amber-400 to-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-4 h-4 bg-amber-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 213,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 212,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 font-medium\",\n                                                                            children: \"PROGRESS\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl xl:text-3xl font-bold text-amber-600 mb-1\",\n                                                                    children: [\n                                                                        Math.round(currentEmissions / 200 * 100),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Monthly Target\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card-glass p-6 relative overflow-hidden group hover:shadow-lg transition-all duration-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-emerald-400 to-emerald-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-4 h-4 bg-emerald-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 227,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 226,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 font-medium\",\n                                                                            children: \"REMAINING\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 229,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl xl:text-3xl font-bold text-emerald-600 mb-1\",\n                                                                    children: 200 - currentEmissions\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"kg CO₂ Left\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-7 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-glass p-6 xl:p-8 h-80\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg xl:text-xl font-semibold text-gray-800 mb-1\",\n                                                            children: \"Emission Trends\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Your carbon footprint over time\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-64\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_line_chart__WEBPACK_IMPORTED_MODULE_3__.ShadcnLineChart, {\n                                                        data: consumptionData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-96\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_history__WEBPACK_IMPORTED_MODULE_6__.JourneyHistory, {\n                                                journeys: journeys\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-12 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-glass p-6 xl:p-8 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 right-0 w-48 h-48 bg-gradient-to-br from-blue-500/5 to-purple-600/5 rounded-full blur-3xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl xl:text-2xl font-semibold text-gray-800 mb-2\",\n                                                                children: \"Community Leaderboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"See how you compare with other eco-warriors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leaderboard__WEBPACK_IMPORTED_MODULE_4__.Leaderboard, {\n                                                        users: leaderboardData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"5h/zXXvEFb0Ro3X+OlKvuaTEMyA=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});