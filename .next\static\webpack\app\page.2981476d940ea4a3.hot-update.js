"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/journey-history.tsx":
/*!****************************************!*\
  !*** ./components/journey-history.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JourneyHistory: () => (/* binding */ JourneyHistory)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mock-journeys */ \"(app-pages-browser)/./lib/mock-journeys.ts\");\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiChevronLeft,FiChevronRight!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ JourneyHistory auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction JourneyHistory(param) {\n    let { journeys, className = \"\" } = param;\n    _s();\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('daily');\n    const [currentDate, setCurrentDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const periodButtons = [\n        {\n            period: 'daily',\n            label: 'Daily'\n        },\n        {\n            period: 'weekly',\n            label: 'Weekly'\n        },\n        {\n            period: 'monthly',\n            label: 'Monthly'\n        },\n        {\n            period: 'yearly',\n            label: 'Yearly'\n        }\n    ];\n    const filteredJourneys = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"JourneyHistory.useMemo[filteredJourneys]\": ()=>(0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__.filterJourneysByPeriod)(journeys, selectedPeriod, currentDate)\n    }[\"JourneyHistory.useMemo[filteredJourneys]\"], [\n        journeys,\n        selectedPeriod,\n        currentDate\n    ]);\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"JourneyHistory.useMemo[stats]\": ()=>(0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__.calculateJourneyStats)(filteredJourneys)\n    }[\"JourneyHistory.useMemo[stats]\"], [\n        filteredJourneys\n    ]);\n    const navigateDate = (direction)=>{\n        const newDate = new Date(currentDate);\n        switch(selectedPeriod){\n            case 'daily':\n                newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));\n                break;\n            case 'weekly':\n                newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));\n                break;\n            case 'monthly':\n                newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));\n                break;\n            case 'yearly':\n                newDate.setFullYear(newDate.getFullYear() + (direction === 'next' ? 1 : -1));\n                break;\n        }\n        setCurrentDate(newDate);\n    };\n    const formatJourneyTime = (timestamp)=>{\n        return timestamp.toLocaleTimeString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    const formatJourneyDate = (timestamp)=>{\n        if (selectedPeriod === 'daily') {\n            return formatJourneyTime(timestamp);\n        }\n        return timestamp.toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            ...selectedPeriod === 'yearly' ? {\n                year: 'numeric'\n            } : {}\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-glass p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCalendar, {\n                                        className: \"w-5 h-5 text-emerald-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-800\",\n                                        children: \"Journey History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: periodButtons.map((param)=>{\n                                    let { period, label } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: selectedPeriod === period ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedPeriod(period),\n                                        className: \"\".concat(selectedPeriod === period ? 'bg-emerald-500 text-white' : 'bg-white/60 text-gray-700 border-gray-300'),\n                                        children: label\n                                    }, period, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-4 pt-4 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>navigateDate('prev'),\n                                className: \"bg-white/60 border-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiChevronLeft, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800\",\n                                    children: (0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_3__.formatDateForPeriod)(currentDate, selectedPeriod)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>navigateDate('next'),\n                                className: \"bg-white/60 border-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronLeft_FiChevronRight_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiChevronRight, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 sm:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-emerald-600\",\n                                children: stats.totalJourneys\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Journeys\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-600\",\n                                children: stats.totalDistance\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"km Total\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-orange-600\",\n                                children: stats.totalEmissions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"kg CO₂\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-glass p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-purple-600\",\n                                children: stats.averageEmissions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Avg CO₂\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-glass p-4 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FiBarChart3, {\n                                className: \"w-5 h-5 text-emerald-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800\",\n                                children: [\n                                    filteredJourneys.length,\n                                    \" Journey\",\n                                    filteredJourneys.length !== 1 ? 's' : ''\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    filteredJourneys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl mb-2\",\n                                children: \"\\uD83C\\uDF31\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No journeys recorded for this period\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Add a journey to start tracking!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                        children: filteredJourneys.map((journey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full \".concat(journey.transportColor, \" flex items-center justify-center text-white text-lg flex-shrink-0\"),\n                                                children: journey.transportIcon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-800\",\n                                                                children: journey.transportName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    journey.distance,\n                                                                    \" km\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: formatJourneyDate(journey.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right flex-shrink-0 ml-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-semibold text-emerald-600\",\n                                                children: [\n                                                    journey.emissions,\n                                                    \" kg\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"CO₂\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, journey.id, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\carbontracker\\\\components\\\\journey-history.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(JourneyHistory, \"haPjCueP3H1rILyppO5CUx3tOAo=\");\n_c = JourneyHistory;\nvar _c;\n$RefreshReg$(_c, \"JourneyHistory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/journey-history.tsx\n"));

/***/ })

});