"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/modern-gauge.tsx":
/*!*************************************!*\
  !*** ./components/modern-gauge.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModernGauge: () => (/* binding */ ModernGauge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_circular_progressbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-circular-progressbar */ \"(app-pages-browser)/./node_modules/react-circular-progressbar/dist/index.esm.js\");\n/* harmony import */ var react_circular_progressbar_dist_styles_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-circular-progressbar/dist/styles.css */ \"(app-pages-browser)/./node_modules/react-circular-progressbar/dist/styles.css\");\n/* __next_internal_client_entry_do_not_use__ ModernGauge auto */ \n\n\n\nfunction ModernGauge(param) {\n    let { value, max, size = 200, className = \"\", title = \"CO₂ Emissions\", subtitle = \"this month\", showPercentage = true } = param;\n    const percentage = Math.min(value / max * 100, 100);\n    // Dynamic color based on percentage using color theory\n    const getColor = (percent)=>{\n        if (percent <= 25) return '#22c55e' // green-500 - excellent\n        ;\n        if (percent <= 50) return '#84cc16' // lime-500 - good\n        ;\n        if (percent <= 75) return '#f59e0b' // amber-500 - warning\n        ;\n        if (percent <= 90) return '#f97316' // orange-500 - high\n        ;\n        return '#ef4444' // red-500 - critical\n        ;\n    };\n    const getGradientColors = (percent)=>{\n        if (percent <= 25) return {\n            from: '#22c55e',\n            to: '#16a34a'\n        } // green gradient\n        ;\n        if (percent <= 50) return {\n            from: '#84cc16',\n            to: '#65a30d'\n        } // lime gradient\n        ;\n        if (percent <= 75) return {\n            from: '#f59e0b',\n            to: '#d97706'\n        } // amber gradient\n        ;\n        if (percent <= 90) return {\n            from: '#f97316',\n            to: '#ea580c'\n        } // orange gradient\n        ;\n        return {\n            from: '#ef4444',\n            to: '#dc2626'\n        } // red gradient\n        ;\n    };\n    const color = getColor(percentage);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        style: {\n            width: size,\n            height: size\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_circular_progressbar__WEBPACK_IMPORTED_MODULE_2__.CircularProgressbar, {\n                value: percentage,\n                styles: (0,react_circular_progressbar__WEBPACK_IMPORTED_MODULE_2__.buildStyles)({\n                    // Rotation of path and trail, in number of turns (0-1)\n                    rotation: 0.25,\n                    // Whether to use rounded or flat corners on the ends\n                    strokeLinecap: 'round',\n                    // Text size\n                    textSize: '0px',\n                    // How long animation takes to go from one percentage to another, in seconds\n                    pathTransitionDuration: 1.5,\n                    // Colors\n                    pathColor: color,\n                    textColor: color,\n                    trailColor: 'rgba(255, 255, 255, 0.15)',\n                    backgroundColor: 'transparent'\n                }),\n                strokeWidth: 8\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex flex-col items-center justify-center text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold \".concat(size > 180 ? 'text-3xl' : size > 140 ? 'text-2xl' : 'text-xl'),\n                                children: value\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/80 \".concat(size > 180 ? 'text-sm' : 'text-xs'),\n                                children: [\n                                    \"kg \",\n                                    title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    showPercentage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-semibold \".concat(size > 180 ? 'text-lg' : 'text-base'),\n                                style: {\n                                    color\n                                },\n                                children: [\n                                    percentage.toFixed(0),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/70 \".concat(size > 180 ? 'text-xs' : 'text-xs'),\n                                children: [\n                                    \"of \",\n                                    max,\n                                    \"kg target\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/60 text-center mt-1 \".concat(size > 180 ? 'text-xs' : 'text-xs'),\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-full opacity-20 blur-xl\",\n                style: {\n                    background: \"radial-gradient(circle, \".concat(color, \"40 0%, transparent 70%)\"),\n                    transform: 'scale(1.2)'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_c = ModernGauge;\nvar _c;\n$RefreshReg$(_c, \"ModernGauge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/modern-gauge.tsx\n"));

/***/ })

});