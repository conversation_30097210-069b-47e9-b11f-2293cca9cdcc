import { Journey, TransportMode } from '@/types/journey'

// Simple seeded random number generator for consistent results
class SeededRandom {
  private seed: number

  constructor(seed: number) {
    this.seed = seed
  }

  next(): number {
    this.seed = (this.seed * 9301 + 49297) % 233280
    return this.seed / 233280
  }
}

export const transportModes: TransportMode[] = [
  { id: 'bicycle', name: 'Bicycle', icon: '🚲', color: 'bg-emerald-500', emissionFactor: 0 },
  { id: 'walking', name: 'Walking', icon: '🚶', color: 'bg-green-500', emissionFactor: 0 },
  { id: 'bus', name: 'Bus', icon: '🚌', color: 'bg-amber-500', emissionFactor: 0.089 },
  { id: 'train', name: 'Train', icon: '🚊', color: 'bg-blue-500', emissionFactor: 0.041 },
  { id: 'motorbike', name: 'Motorbike', icon: '🏍️', color: 'bg-orange-500', emissionFactor: 0.113 },
  { id: 'car', name: 'Car', icon: '🚗', color: 'bg-slate-500', emissionFactor: 0.171 },
  { id: 'plane', name: 'Plane', icon: '✈️', color: 'bg-red-500', emissionFactor: 0.255 },
]

// Generate mock journeys for different time periods
export function generateMockJourneys(): Journey[] {
  const journeys: Journey[] = []
  const rng = new SeededRandom(12345) // Fixed seed for consistent results
  const baseDate = new Date('2024-01-01') // Fixed base date
  const now = new Date()

  // Generate journeys for the past 365 days from today
  for (let i = 0; i < 365; i++) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)

    // Seeded random number of journeys per day (0-4)
    const journeysPerDay = Math.floor(rng.next() * 5)

    for (let j = 0; j < journeysPerDay; j++) {
      const transport = transportModes[Math.floor(rng.next() * transportModes.length)]
      const distance = Math.floor(rng.next() * 50) + 1 // 1-50 km
      const emissions = transport.emissionFactor * distance

      // Seeded random time during the day
      const hour = Math.floor(rng.next() * 24)
      const minute = Math.floor(rng.next() * 60)
      const journeyTime = new Date(date)
      journeyTime.setHours(hour, minute, 0, 0)

      journeys.push({
        id: `journey-${i}-${j}`,
        transportMode: transport.id,
        transportName: transport.name,
        transportIcon: transport.icon,
        transportColor: transport.color,
        distance,
        emissions: emissions === 0 ? 0 : Number(emissions.toFixed(1)),
        timestamp: journeyTime,
        vehicleType: transport.id === 'car' ? 'medium' : transport.id === 'motorbike' ? '250cc' : undefined
      })
    }
  }

  return journeys.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
}

// Filter journeys by time period
export function filterJourneysByPeriod(journeys: Journey[], period: 'daily' | 'weekly' | 'monthly' | 'yearly', date: Date = new Date()): Journey[] {
  const startDate = new Date(date)
  const endDate = new Date(date)
  
  switch (period) {
    case 'daily':
      startDate.setHours(0, 0, 0, 0)
      endDate.setHours(23, 59, 59, 999)
      break
    case 'weekly':
      const dayOfWeek = startDate.getDay()
      startDate.setDate(startDate.getDate() - dayOfWeek)
      startDate.setHours(0, 0, 0, 0)
      endDate.setDate(startDate.getDate() + 6)
      endDate.setHours(23, 59, 59, 999)
      break
    case 'monthly':
      startDate.setDate(1)
      startDate.setHours(0, 0, 0, 0)
      endDate.setMonth(endDate.getMonth() + 1, 0)
      endDate.setHours(23, 59, 59, 999)
      break
    case 'yearly':
      startDate.setMonth(0, 1)
      startDate.setHours(0, 0, 0, 0)
      endDate.setMonth(11, 31)
      endDate.setHours(23, 59, 59, 999)
      break
  }
  
  return journeys.filter(journey => 
    journey.timestamp >= startDate && journey.timestamp <= endDate
  )
}

// Calculate journey statistics
export function calculateJourneyStats(journeys: Journey[]) {
  const totalJourneys = journeys.length
  const totalDistance = journeys.reduce((sum, journey) => sum + journey.distance, 0)
  const totalEmissions = journeys.reduce((sum, journey) => sum + journey.emissions, 0)
  const averageEmissions = totalJourneys > 0 ? totalEmissions / totalJourneys : 0
  
  return {
    totalJourneys,
    totalDistance,
    totalEmissions: Number(totalEmissions.toFixed(2)),
    averageEmissions: Number(averageEmissions.toFixed(2))
  }
}

// Format date for display
export function formatDateForPeriod(date: Date, period: 'daily' | 'weekly' | 'monthly' | 'yearly'): string {
  switch (period) {
    case 'daily':
      return date.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    case 'weekly':
      const weekStart = new Date(date)
      const dayOfWeek = weekStart.getDay()
      weekStart.setDate(weekStart.getDate() - dayOfWeek)
      const weekEnd = new Date(weekStart)
      weekEnd.setDate(weekEnd.getDate() + 6)
      return `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`
    case 'monthly':
      return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' })
    case 'yearly':
      return date.getFullYear().toString()
  }
}
