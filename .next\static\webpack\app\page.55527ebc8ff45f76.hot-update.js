"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_modern_gauge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/modern-gauge */ \"(app-pages-browser)/./components/modern-gauge.tsx\");\n/* harmony import */ var _components_shadcn_line_chart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shadcn-line-chart */ \"(app-pages-browser)/./components/shadcn-line-chart.tsx\");\n/* harmony import */ var _components_leaderboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/leaderboard */ \"(app-pages-browser)/./components/leaderboard.tsx\");\n/* harmony import */ var _components_journey_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/journey-form */ \"(app-pages-browser)/./components/journey-form.tsx\");\n/* harmony import */ var _components_journey_history__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/journey-history */ \"(app-pages-browser)/./components/journey-history.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_FiPlus_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FiPlus!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_mock_journeys__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/mock-journeys */ \"(app-pages-browser)/./lib/mock-journeys.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [currentEmissions, setCurrentEmissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(120);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [journeys, setJourneys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Home.useState\": ()=>(0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_10__.generateMockJourneys)()\n    }[\"Home.useState\"]);\n    // Sample data\n    const consumptionData = [\n        {\n            month: 'Jan',\n            value: 520\n        },\n        {\n            month: 'Feb',\n            value: 480\n        },\n        {\n            month: 'Mar',\n            value: 620\n        },\n        {\n            month: 'Apr',\n            value: 380\n        },\n        {\n            month: 'May',\n            value: 450\n        },\n        {\n            month: 'Jun',\n            value: 590\n        },\n        {\n            month: 'Jul',\n            value: 680\n        },\n        {\n            month: 'Aug',\n            value: 520\n        },\n        {\n            month: 'Sep',\n            value: 380\n        },\n        {\n            month: 'Oct',\n            value: 680\n        },\n        {\n            month: 'Nov',\n            value: 590\n        },\n        {\n            month: 'Dec',\n            value: 740\n        }\n    ];\n    const leaderboardData = [\n        {\n            id: 1,\n            name: 'Anita',\n            emissions: 450,\n            avatar: '/avatars/anita.jpg'\n        },\n        {\n            id: 2,\n            name: 'Diana',\n            emissions: 464,\n            avatar: '/avatars/diana.jpg'\n        },\n        {\n            id: 3,\n            name: 'Lucia',\n            emissions: 532,\n            avatar: '/avatars/lucia.jpg'\n        },\n        {\n            id: 4,\n            name: 'Oliver Deak',\n            emissions: 740,\n            avatar: '/avatars/oliver.jpg'\n        }\n    ];\n    const handleJourneySubmit = (data)=>{\n        const transportMode = _lib_mock_journeys__WEBPACK_IMPORTED_MODULE_10__.transportModes.find((mode)=>mode.id === data.transport);\n        if (transportMode) {\n            const newJourney = {\n                id: \"journey-new-\".concat(journeys.length),\n                transportMode: data.transport,\n                transportName: transportMode.name,\n                transportIcon: transportMode.icon,\n                transportColor: transportMode.color,\n                distance: data.mileage,\n                emissions: data.emissions === 0 ? 0 : Number(data.emissions.toFixed(1)),\n                timestamp: new Date(),\n                vehicleType: data.type\n            };\n            setJourneys((prev)=>[\n                    newJourney,\n                    ...prev\n                ]);\n            setCurrentEmissions((prev)=>prev + data.emissions);\n        }\n        setIsModalOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-mesh-gradient\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden p-4 sm:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-sm sm:max-w-md mx-auto space-y-4 sm:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl p-4 sm:p-6 text-white shadow-xl shadow-emerald-500/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4 sm:mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl sm:text-2xl font-bold\",\n                                                    children: \"Hello, Sarah\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-emerald-100 text-xs sm:text-sm\",\n                                                    children: \"Your emission is going well \\uD83C\\uDF31\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                            className: \"w-12 h-12 sm:w-14 sm:h-14 border-2 sm:border-3 border-white/30 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarImage, {\n                                                    src: \"/avatars/sarah.jpg\",\n                                                    alt: \"Sarah\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarFallback, {\n                                                    className: \"bg-emerald-600 text-white font-semibold text-sm sm:text-base\",\n                                                    children: \"S\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-4 sm:mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modern_gauge__WEBPACK_IMPORTED_MODULE_2__.ModernGauge, {\n                                        value: currentEmissions,\n                                        max: 200,\n                                        size: 160\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl sm:text-2xl font-bold text-white\",\n                                                    children: [\n                                                        Math.round(currentEmissions / 200 * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-emerald-100\",\n                                                    children: \"Monthly Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl sm:text-2xl font-bold text-white\",\n                                                    children: 200 - currentEmissions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-emerald-100\",\n                                                    children: \"kg Remaining\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                                    open: isModalOpen,\n                                    onOpenChange: setIsModalOpen,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                className: \"w-full glass text-white font-semibold py-3 sm:py-4 rounded-xl sm:rounded-2xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiPlus_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiPlus, {\n                                                        className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Add Journey\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                            className: \"sm:max-w-lg mx-4 max-h-[90vh] overflow-y-auto rounded-3xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_form__WEBPACK_IMPORTED_MODULE_5__.JourneyForm, {\n                                                onSubmit: handleJourneySubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-glass\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_line_chart__WEBPACK_IMPORTED_MODULE_3__.ShadcnLineChart, {\n                                data: consumptionData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_history__WEBPACK_IMPORTED_MODULE_6__.JourneyHistory, {\n                            journeys: journeys\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-glass p-4 sm:p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leaderboard__WEBPACK_IMPORTED_MODULE_4__.Leaderboard, {\n                                users: leaderboardData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block p-6 xl:p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                            className: \"w-14 h-14 border-2 border-emerald-200 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarImage, {\n                                                    src: \"/avatars/sarah.jpg\",\n                                                    alt: \"Sarah\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarFallback, {\n                                                    className: \"bg-emerald-500 text-white font-bold\",\n                                                    children: \"S\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-gray-800\",\n                                                    children: \"Hello, Sarah\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Your emission tracking is going well \\uD83C\\uDF31\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                                    open: isModalOpen,\n                                    onOpenChange: setIsModalOpen,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                className: \"bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold px-6 py-3 rounded-xl shadow-lg transition-all duration-300 hover:scale-105\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiPlus_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiPlus, {\n                                                        className: \"w-5 h-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Add Journey\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                            className: \"sm:max-w-lg max-h-[90vh] overflow-y-auto rounded-3xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_form__WEBPACK_IMPORTED_MODULE_5__.JourneyForm, {\n                                                onSubmit: handleJourneySubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-12 gap-6 xl:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-5 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl p-6 xl:p-8 text-white shadow-2xl shadow-emerald-500/25 relative overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-20 -right-20 w-40 h-40 bg-white/10 rounded-full blur-2xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -bottom-10 -left-10 w-32 h-32 bg-white/5 rounded-full blur-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-xl xl:text-2xl font-bold mb-1\",\n                                                                    children: \"Monthly Progress\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-emerald-100 text-sm\",\n                                                                    children: \"Track your carbon footprint\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-center mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modern_gauge__WEBPACK_IMPORTED_MODULE_2__.ModernGauge, {\n                                                                value: currentEmissions,\n                                                                max: 200,\n                                                                size: 220\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card-glass p-6 relative overflow-hidden group hover:shadow-lg transition-all duration-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-amber-400 to-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-4 h-4 bg-amber-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 189,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 188,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 font-medium\",\n                                                                            children: \"PROGRESS\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 191,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl xl:text-3xl font-bold text-amber-600 mb-1\",\n                                                                    children: [\n                                                                        Math.round(currentEmissions / 200 * 100),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Monthly Target\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card-glass p-6 relative overflow-hidden group hover:shadow-lg transition-all duration-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-emerald-400 to-emerald-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-4 h-4 bg-emerald-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 203,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 202,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 font-medium\",\n                                                                            children: \"REMAINING\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl xl:text-3xl font-bold text-emerald-600 mb-1\",\n                                                                    children: 200 - currentEmissions\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"kg CO₂ Left\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-7 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-glass p-6 xl:p-8 h-80\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg xl:text-xl font-semibold text-gray-800 mb-1\",\n                                                            children: \"Emission Trends\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Your carbon footprint over time\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-64\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_line_chart__WEBPACK_IMPORTED_MODULE_3__.ShadcnLineChart, {\n                                                        data: consumptionData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-96\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_history__WEBPACK_IMPORTED_MODULE_6__.JourneyHistory, {\n                                                journeys: journeys\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-12 mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-glass p-6 xl:p-8 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 right-0 w-48 h-48 bg-gradient-to-br from-blue-500/5 to-purple-600/5 rounded-full blur-3xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl xl:text-2xl font-semibold text-gray-800 mb-2\",\n                                                                children: \"Community Leaderboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"See how you compare with other eco-warriors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leaderboard__WEBPACK_IMPORTED_MODULE_4__.Leaderboard, {\n                                                        users: leaderboardData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"5h/zXXvEFb0Ro3X+OlKvuaTEMyA=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});