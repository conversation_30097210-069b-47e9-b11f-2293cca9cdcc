"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/modern-gauge.tsx":
/*!*************************************!*\
  !*** ./components/modern-gauge.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModernGauge: () => (/* binding */ ModernGauge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_circular_progressbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-circular-progressbar */ \"(app-pages-browser)/./node_modules/react-circular-progressbar/dist/index.esm.js\");\n/* harmony import */ var react_circular_progressbar_dist_styles_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-circular-progressbar/dist/styles.css */ \"(app-pages-browser)/./node_modules/react-circular-progressbar/dist/styles.css\");\n/* __next_internal_client_entry_do_not_use__ ModernGauge auto */ \n\n\n\nfunction ModernGauge(param) {\n    let { value, max, size = 200, className = \"\", title = \"CO₂ Emissions\", subtitle = \"this month\", showPercentage = true } = param;\n    const percentage = Math.min(value / max * 100, 100);\n    // Dynamic color based on percentage using color theory\n    const getColor = (percent)=>{\n        if (percent <= 25) return '#22c55e' // green-500 - excellent\n        ;\n        if (percent <= 50) return '#84cc16' // lime-500 - good\n        ;\n        if (percent <= 75) return '#f59e0b' // amber-500 - warning\n        ;\n        if (percent <= 90) return '#f97316' // orange-500 - high\n        ;\n        return '#ef4444' // red-500 - critical\n        ;\n    };\n    const getGradientColors = (percent)=>{\n        if (percent <= 25) return {\n            from: '#22c55e',\n            to: '#16a34a'\n        } // green gradient\n        ;\n        if (percent <= 50) return {\n            from: '#84cc16',\n            to: '#65a30d'\n        } // lime gradient\n        ;\n        if (percent <= 75) return {\n            from: '#f59e0b',\n            to: '#d97706'\n        } // amber gradient\n        ;\n        if (percent <= 90) return {\n            from: '#f97316',\n            to: '#ea580c'\n        } // orange gradient\n        ;\n        return {\n            from: '#ef4444',\n            to: '#dc2626'\n        } // red gradient\n        ;\n    };\n    const color = getColor(percentage);\n    const gradientColors = getGradientColors(percentage);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        style: {\n            width: size,\n            height: size\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"absolute inset-0\",\n                style: {\n                    width: 0,\n                    height: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"gauge-gradient-\".concat(size),\n                        x1: \"0%\",\n                        y1: \"0%\",\n                        x2: \"100%\",\n                        y2: \"100%\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0%\",\n                                stopColor: gradientColors.from\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"100%\",\n                                stopColor: gradientColors.to\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_circular_progressbar__WEBPACK_IMPORTED_MODULE_2__.CircularProgressbar, {\n                value: percentage,\n                styles: (0,react_circular_progressbar__WEBPACK_IMPORTED_MODULE_2__.buildStyles)({\n                    // Rotation of path and trail, in number of turns (0-1)\n                    rotation: 0.25,\n                    // Whether to use rounded or flat corners on the ends\n                    strokeLinecap: 'round',\n                    // Text size\n                    textSize: '0px',\n                    // How long animation takes to go from one percentage to another, in seconds\n                    pathTransitionDuration: 1.5,\n                    // Colors\n                    pathColor: \"url(#gauge-gradient-\".concat(size, \")\"),\n                    textColor: color,\n                    trailColor: 'rgba(255, 255, 255, 0.2)',\n                    backgroundColor: 'transparent'\n                }),\n                strokeWidth: 10\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex flex-col items-center justify-center text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold \".concat(size > 180 ? 'text-3xl' : size > 140 ? 'text-2xl' : 'text-xl'),\n                                children: value\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/80 \".concat(size > 180 ? 'text-sm' : 'text-xs'),\n                                children: [\n                                    \"kg \",\n                                    title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    showPercentage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-semibold \".concat(size > 180 ? 'text-lg' : 'text-base'),\n                                style: {\n                                    color\n                                },\n                                children: [\n                                    percentage.toFixed(0),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/70 \".concat(size > 180 ? 'text-xs' : 'text-xs'),\n                                children: [\n                                    \"of \",\n                                    max,\n                                    \"kg target\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/60 text-center mt-1 \".concat(size > 180 ? 'text-xs' : 'text-xs'),\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-full opacity-20 blur-xl\",\n                style: {\n                    background: \"radial-gradient(circle, \".concat(color, \"40 0%, transparent 70%)\"),\n                    transform: 'scale(1.2)'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\carbontracker\\\\components\\\\modern-gauge.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_c = ModernGauge;\nvar _c;\n$RefreshReg$(_c, \"ModernGauge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/modern-gauge.tsx\n"));

/***/ })

});