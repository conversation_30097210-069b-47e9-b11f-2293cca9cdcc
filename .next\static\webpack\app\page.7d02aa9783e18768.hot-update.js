"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/shadcn-line-chart.tsx":
/*!******************************************!*\
  !*** ./components/shadcn-line-chart.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShadcnLineChart: () => (/* binding */ ShadcnLineChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./components/ui/chart.tsx\");\n/* __next_internal_client_entry_do_not_use__ ShadcnLineChart auto */ \n\n\n\nconst chartConfig = {\n    value: {\n        label: \"CO₂ Emissions\",\n        color: \"hsl(var(--chart-1))\"\n    }\n};\nfunction ShadcnLineChart(param) {\n    let { data, className = \"\" } = param;\n    var _data_;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 sm:p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg sm:text-xl font-semibold text-gray-800 mb-4 sm:mb-6\",\n                children: \"Consumption Trend\"\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartContainer, {\n                config: chartConfig,\n                className: \"min-h-[200px] sm:min-h-[250px] lg:min-h-[280px] w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.LineChart, {\n                    accessibilityLayer: true,\n                    data: data,\n                    margin: {\n                        left: 16,\n                        right: 16,\n                        top: 16,\n                        bottom: 16\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {\n                            vertical: false\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {\n                            dataKey: \"month\",\n                            tickLine: false,\n                            axisLine: false,\n                            tickMargin: 6,\n                            tick: {\n                                fontSize: 12\n                            },\n                            tickFormatter: (value)=>value.slice(0, 3)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {\n                            tickLine: false,\n                            axisLine: false,\n                            tickMargin: 6,\n                            tick: {\n                                fontSize: 12\n                            },\n                            tickFormatter: (value)=>\"\".concat(value, \"kg\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartTooltip, {\n                            cursor: false,\n                            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_2__.ChartTooltipContent, {\n                                hideLabel: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 22\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Line, {\n                            dataKey: \"value\",\n                            type: \"monotone\",\n                            stroke: \"var(--color-value)\",\n                            strokeWidth: 2,\n                            dot: {\n                                fill: \"var(--color-value)\",\n                                strokeWidth: 1,\n                                r: 3\n                            },\n                            activeDot: {\n                                r: 5,\n                                strokeWidth: 1\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center mt-3 sm:mt-4 mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 sm:w-3 h-px bg-gray-300 mr-2 sm:mr-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs sm:text-sm text-gray-500 font-medium\",\n                        children: \"Global Average: 760kg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 sm:w-3 h-px bg-gray-300 ml-2 sm:ml-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-right\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs sm:text-sm text-gray-600 font-semibold\",\n                    children: [\n                        ((_data_ = data[data.length - 1]) === null || _data_ === void 0 ? void 0 : _data_.value) || 0,\n                        \" kg CO₂\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\carbontracker\\\\components\\\\shadcn-line-chart.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c = ShadcnLineChart;\nvar _c;\n$RefreshReg$(_c, \"ShadcnLineChart\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/shadcn-line-chart.tsx\n"));

/***/ })

});