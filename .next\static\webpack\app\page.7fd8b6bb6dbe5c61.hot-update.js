"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/mock-journeys.ts":
/*!******************************!*\
  !*** ./lib/mock-journeys.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateJourneyStats: () => (/* binding */ calculateJourneyStats),\n/* harmony export */   filterJourneysByPeriod: () => (/* binding */ filterJourneysByPeriod),\n/* harmony export */   formatDateForPeriod: () => (/* binding */ formatDateForPeriod),\n/* harmony export */   generateMockJourneys: () => (/* binding */ generateMockJourneys),\n/* harmony export */   transportModes: () => (/* binding */ transportModes)\n/* harmony export */ });\n// Simple seeded random number generator for consistent results\nclass SeededRandom {\n    next() {\n        this.seed = (this.seed * 9301 + 49297) % 233280;\n        return this.seed / 233280;\n    }\n    constructor(seed){\n        this.seed = seed;\n    }\n}\nconst transportModes = [\n    {\n        id: 'bicycle',\n        name: 'Bicycle',\n        icon: '🚲',\n        color: 'bg-blue-400',\n        emissionFactor: 0\n    },\n    {\n        id: 'walking',\n        name: 'Walking',\n        icon: '🚶',\n        color: 'bg-green-400',\n        emissionFactor: 0\n    },\n    {\n        id: 'bus',\n        name: 'Bus',\n        icon: '🚌',\n        color: 'bg-yellow-400',\n        emissionFactor: 0.089\n    },\n    {\n        id: 'train',\n        name: 'Train',\n        icon: '🚊',\n        color: 'bg-orange-400',\n        emissionFactor: 0.041\n    },\n    {\n        id: 'motorbike',\n        name: 'Motorbike',\n        icon: '🏍️',\n        color: 'bg-red-400',\n        emissionFactor: 0.113\n    },\n    {\n        id: 'car',\n        name: 'Car',\n        icon: '🚗',\n        color: 'bg-gray-400',\n        emissionFactor: 0.171\n    },\n    {\n        id: 'plane',\n        name: 'Plane',\n        icon: '✈️',\n        color: 'bg-purple-400',\n        emissionFactor: 0.255\n    }\n];\n// Generate mock journeys for different time periods\nfunction generateMockJourneys() {\n    const journeys = [];\n    const rng = new SeededRandom(12345) // Fixed seed for consistent results\n    ;\n    const baseDate = new Date('2024-01-01') // Fixed base date\n    ;\n    const now = new Date();\n    // Generate journeys for the past 365 days from today\n    for(let i = 0; i < 365; i++){\n        const date = new Date(now);\n        date.setDate(date.getDate() - i);\n        // Seeded random number of journeys per day (0-4)\n        const journeysPerDay = Math.floor(rng.next() * 5);\n        for(let j = 0; j < journeysPerDay; j++){\n            const transport = transportModes[Math.floor(rng.next() * transportModes.length)];\n            const distance = Math.floor(rng.next() * 50) + 1 // 1-50 km\n            ;\n            const emissions = transport.emissionFactor * distance;\n            // Seeded random time during the day\n            const hour = Math.floor(rng.next() * 24);\n            const minute = Math.floor(rng.next() * 60);\n            const journeyTime = new Date(date);\n            journeyTime.setHours(hour, minute, 0, 0);\n            journeys.push({\n                id: \"journey-\".concat(i, \"-\").concat(j),\n                transportMode: transport.id,\n                transportName: transport.name,\n                transportIcon: transport.icon,\n                transportColor: transport.color,\n                distance,\n                emissions: Number(emissions.toFixed(2)),\n                timestamp: journeyTime,\n                vehicleType: transport.id === 'car' ? 'medium' : transport.id === 'motorbike' ? '250cc' : undefined\n            });\n        }\n    }\n    return journeys.sort((a, b)=>b.timestamp.getTime() - a.timestamp.getTime());\n}\n// Filter journeys by time period\nfunction filterJourneysByPeriod(journeys, period) {\n    let date = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : new Date();\n    const startDate = new Date(date);\n    const endDate = new Date(date);\n    switch(period){\n        case 'daily':\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'weekly':\n            const dayOfWeek = startDate.getDay();\n            startDate.setDate(startDate.getDate() - dayOfWeek);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setDate(startDate.getDate() + 6);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'monthly':\n            startDate.setDate(1);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setMonth(endDate.getMonth() + 1, 0);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n        case 'yearly':\n            startDate.setMonth(0, 1);\n            startDate.setHours(0, 0, 0, 0);\n            endDate.setMonth(11, 31);\n            endDate.setHours(23, 59, 59, 999);\n            break;\n    }\n    return journeys.filter((journey)=>journey.timestamp >= startDate && journey.timestamp <= endDate);\n}\n// Calculate journey statistics\nfunction calculateJourneyStats(journeys) {\n    const totalJourneys = journeys.length;\n    const totalDistance = journeys.reduce((sum, journey)=>sum + journey.distance, 0);\n    const totalEmissions = journeys.reduce((sum, journey)=>sum + journey.emissions, 0);\n    const averageEmissions = totalJourneys > 0 ? totalEmissions / totalJourneys : 0;\n    return {\n        totalJourneys,\n        totalDistance,\n        totalEmissions: Number(totalEmissions.toFixed(2)),\n        averageEmissions: Number(averageEmissions.toFixed(2))\n    };\n}\n// Format date for display\nfunction formatDateForPeriod(date, period) {\n    switch(period){\n        case 'daily':\n            return date.toLocaleDateString('en-US', {\n                weekday: 'long',\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n            });\n        case 'weekly':\n            const weekStart = new Date(date);\n            const dayOfWeek = weekStart.getDay();\n            weekStart.setDate(weekStart.getDate() - dayOfWeek);\n            const weekEnd = new Date(weekStart);\n            weekEnd.setDate(weekEnd.getDate() + 6);\n            return \"\".concat(weekStart.toLocaleDateString('en-US', {\n                month: 'short',\n                day: 'numeric'\n            }), \" - \").concat(weekEnd.toLocaleDateString('en-US', {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric'\n            }));\n        case 'monthly':\n            return date.toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long'\n            });\n        case 'yearly':\n            return date.getFullYear().toString();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/mock-journeys.ts\n"));

/***/ })

});