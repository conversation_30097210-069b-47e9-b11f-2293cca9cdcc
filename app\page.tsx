"use client"

import React, { useState, useMemo } from 'react'
import { ModernGauge } from '@/components/modern-gauge'
import { ShadcnLineChart } from '@/components/shadcn-line-chart'
import { Leaderboard } from '@/components/leaderboard'
import { JourneyForm } from '@/components/journey-form'
import { JourneyHistory } from '@/components/journey-history'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { FiPlus } from 'react-icons/fi'
import { Journey } from '@/types/journey'
import { generateMockJourneys, transportModes } from '@/lib/mock-journeys'

export default function Home() {
  const [currentEmissions, setCurrentEmissions] = useState(120)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [journeys, setJourneys] = useState<Journey[]>(() => generateMockJourneys())

  // Sample data
  const consumptionData = [
    { month: 'Jan', value: 520 },
    { month: 'Feb', value: 480 },
    { month: 'Mar', value: 620 },
    { month: 'Apr', value: 380 },
    { month: 'May', value: 450 },
    { month: 'Jun', value: 590 },
    { month: 'Jul', value: 680 },
    { month: 'Aug', value: 520 },
    { month: 'Sep', value: 380 },
    { month: 'Oct', value: 680 },
    { month: 'Nov', value: 590 },
    { month: 'Dec', value: 740 },
  ]

  const leaderboardData = [
    { id: 1, name: 'Anita', emissions: 450, avatar: '/avatars/anita.jpg' },
    { id: 2, name: 'Diana', emissions: 464, avatar: '/avatars/diana.jpg' },
    { id: 3, name: 'Lucia', emissions: 532, avatar: '/avatars/lucia.jpg' },
    { id: 4, name: 'Oliver Deak', emissions: 740, avatar: '/avatars/oliver.jpg' },
  ]

  const handleJourneySubmit = (data: { transport: string; mileage: number; type: string; emissions: number }) => {
    const transportMode = transportModes.find(mode => mode.id === data.transport)

    if (transportMode) {
      const newJourney: Journey = {
        id: `journey-new-${journeys.length}`,
        transportMode: data.transport,
        transportName: transportMode.name,
        transportIcon: transportMode.icon,
        transportColor: transportMode.color,
        distance: data.mileage,
        emissions: data.emissions === 0 ? 0 : Number(data.emissions.toFixed(1)),
        timestamp: new Date(),
        vehicleType: data.type
      }

      setJourneys(prev => [newJourney, ...prev])
      setCurrentEmissions(prev => prev + data.emissions)
    }

    setIsModalOpen(false)
  }

  return (
    <div className="min-h-screen bg-mesh-gradient">
      {/* Mobile Layout */}
      <div className="lg:hidden p-4 sm:p-6">
        <div className="max-w-sm sm:max-w-md mx-auto space-y-4 sm:space-y-6">
          {/* Header Card */}
          <div className="bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl p-4 sm:p-6 text-white shadow-xl shadow-emerald-500/20">
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <div>
                <h1 className="text-xl sm:text-2xl font-bold">Hello, Sarah</h1>
                <p className="text-emerald-100 text-xs sm:text-sm">Your emission is going well 🌱</p>
              </div>
              <Avatar className="w-12 h-12 sm:w-14 sm:h-14 border-2 sm:border-3 border-white/30 shadow-lg">
                <AvatarImage src="/avatars/sarah.jpg" alt="Sarah" />
                <AvatarFallback className="bg-emerald-600 text-white font-semibold text-sm sm:text-base">S</AvatarFallback>
              </Avatar>
            </div>

            {/* Gauge Section */}
            <div className="flex justify-center mb-4 sm:mb-6">
              <ModernGauge value={currentEmissions} max={200} size={160} />
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6">
              <div className="glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-white">{Math.round((currentEmissions / 200) * 100)}%</div>
                <div className="text-xs text-emerald-100">Monthly Progress</div>
              </div>
              <div className="glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-white">{200 - currentEmissions}</div>
                <div className="text-xs text-emerald-100">kg Remaining</div>
              </div>
            </div>

            {/* Add Journey Button */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
              <DialogTrigger asChild>
                <Button className="w-full glass text-white font-semibold py-3 sm:py-4 rounded-xl sm:rounded-2xl">
                  <FiPlus className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  Add Journey
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-lg mx-4 max-h-[90vh] overflow-y-auto rounded-3xl border-2 border-gray-200">
                <JourneyForm onSubmit={handleJourneySubmit} />
              </DialogContent>
            </Dialog>
          </div>

          {/* Chart Card */}
          <div className="card-glass">
            <ShadcnLineChart data={consumptionData} />
          </div>

          {/* Journey History */}
          <JourneyHistory journeys={journeys} />

          {/* Leaderboard Card */}
          <div className="card-glass p-4 sm:p-6">
            <Leaderboard users={leaderboardData} />
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between card-glass p-6">
              <div>
                <h1 className="text-4xl font-bold text-gray-800">Hello, Sarah</h1>
                <p className="text-gray-600 mt-2 text-lg">Your emission tracking is going well 🌱</p>
              </div>
              <Avatar className="w-20 h-20 border-4 border-emerald-200 shadow-glow-emerald">
                <AvatarImage src="/avatars/sarah.jpg" alt="Sarah" />
                <AvatarFallback className="bg-emerald-500 text-white font-bold text-xl">S</AvatarFallback>
              </Avatar>
            </div>
          </div>

          <div className="grid grid-cols-12 gap-8">
            {/* Left Panel - Main Gauge */}
            <div className="col-span-4">
              <div className="bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl p-8 text-white shadow-2xl shadow-emerald-500/25 h-full flex flex-col animate-pulse-soft">
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold mb-2">Monthly Progress</h2>
                  <p className="text-emerald-100">Track your carbon footprint</p>
                </div>

                {/* Modern Gauge */}
                <div className="flex justify-center mb-8 flex-1 items-center">
                  <ModernGauge value={currentEmissions} max={200} size={280} />
                </div>

                {/* Add Journey Button */}
                <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                  <DialogTrigger asChild>
                    <Button className="w-full glass text-white font-semibold py-4 rounded-2xl text-lg shadow-glow-emerald">
                      <FiPlus className="w-6 h-6 mr-3" />
                      Add Journey
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto rounded-3xl border-2 border-gray-200">
                    <JourneyForm onSubmit={handleJourneySubmit} />
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            {/* Middle Panel - Charts and Stats */}
            <div className="col-span-4 space-y-6">
              {/* Quick Stats Row */}
              <div className="grid grid-cols-2 gap-4">
                <div className="card-glass p-6 border-l-4 border-amber-500">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-amber-600">{Math.round((currentEmissions / 200) * 100)}%</div>
                    <div className="text-sm text-gray-600 mt-1">Monthly Progress</div>
                  </div>
                </div>
                <div className="card-glass p-6 border-l-4 border-emerald-500">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-emerald-600">{200 - currentEmissions}</div>
                    <div className="text-sm text-gray-600 mt-1">kg Remaining</div>
                  </div>
                </div>
              </div>

              {/* Chart Card */}
              <div className="card-glass">
                <ShadcnLineChart data={consumptionData} />
              </div>
            </div>

            {/* Right Panel - Journey History */}
            <div className="col-span-4">
              <JourneyHistory journeys={journeys} />
            </div>

            {/* Bottom Panel - Leaderboard */}
            <div className="col-span-12 mt-6">
              <div className="card-glass p-6">
                <Leaderboard users={leaderboardData} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
