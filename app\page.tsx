"use client"

import React, { useState, useMemo } from 'react'
import { ModernGauge } from '@/components/modern-gauge'
import { ShadcnLineChart } from '@/components/shadcn-line-chart'
import { Leaderboard } from '@/components/leaderboard'
import { JourneyForm } from '@/components/journey-form'
import { JourneyHistory } from '@/components/journey-history'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { FiPlus } from 'react-icons/fi'
import { Journey } from '@/types/journey'
import { generateMockJourneys, transportModes } from '@/lib/mock-journeys'

export default function Home() {
  const [currentEmissions, setCurrentEmissions] = useState(120)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [journeys, setJourneys] = useState<Journey[]>(() => generateMockJourneys())

  // Sample data
  const consumptionData = [
    { month: 'Jan', value: 520 },
    { month: 'Feb', value: 480 },
    { month: 'Mar', value: 620 },
    { month: 'Apr', value: 380 },
    { month: 'May', value: 450 },
    { month: 'Jun', value: 590 },
    { month: 'Jul', value: 680 },
    { month: 'Aug', value: 520 },
    { month: 'Sep', value: 380 },
    { month: 'Oct', value: 680 },
    { month: 'Nov', value: 590 },
    { month: 'Dec', value: 740 },
  ]

  const leaderboardData = [
    { id: 1, name: 'Anita', emissions: 450, avatar: '/avatars/anita.jpg' },
    { id: 2, name: 'Diana', emissions: 464, avatar: '/avatars/diana.jpg' },
    { id: 3, name: 'Lucia', emissions: 532, avatar: '/avatars/lucia.jpg' },
    { id: 4, name: 'Oliver Deak', emissions: 740, avatar: '/avatars/oliver.jpg' },
  ]

  const handleJourneySubmit = (data: { transport: string; mileage: number; type: string; emissions: number }) => {
    const transportMode = transportModes.find(mode => mode.id === data.transport)

    if (transportMode) {
      const newJourney: Journey = {
        id: `journey-new-${journeys.length}`,
        transportMode: data.transport,
        transportName: transportMode.name,
        transportIcon: transportMode.icon,
        transportColor: transportMode.color,
        distance: data.mileage,
        emissions: data.emissions === 0 ? 0 : Number(data.emissions.toFixed(1)),
        timestamp: new Date(),
        vehicleType: data.type
      }

      setJourneys(prev => [newJourney, ...prev])
      setCurrentEmissions(prev => prev + data.emissions)
    }

    setIsModalOpen(false)
  }

  return (
    <div className="min-h-screen bg-mesh-gradient">
      {/* Mobile Layout */}
      <div className="lg:hidden p-4 sm:p-6">
        <div className="max-w-sm sm:max-w-md mx-auto space-y-4 sm:space-y-6">
          {/* Header Card */}
          <div className="bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl p-4 sm:p-6 text-white shadow-xl shadow-emerald-500/20">
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <div>
                <h1 className="text-xl sm:text-2xl font-bold">Hello, Sarah</h1>
                <p className="text-emerald-100 text-xs sm:text-sm">Your emission is going well 🌱</p>
              </div>
              <Avatar className="w-12 h-12 sm:w-14 sm:h-14 border-2 sm:border-3 border-white/30 shadow-lg">
                <AvatarImage src="/avatars/sarah.jpg" alt="Sarah" />
                <AvatarFallback className="bg-emerald-600 text-white font-semibold text-sm sm:text-base">S</AvatarFallback>
              </Avatar>
            </div>

            {/* Gauge Section */}
            <div className="flex justify-center mb-4 sm:mb-6">
              <ModernGauge value={currentEmissions} max={200} size={160} />
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6">
              <div className="glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-white">{Math.round((currentEmissions / 200) * 100)}%</div>
                <div className="text-xs text-emerald-100">Monthly Progress</div>
              </div>
              <div className="glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-white">{200 - currentEmissions}</div>
                <div className="text-xs text-emerald-100">kg Remaining</div>
              </div>
            </div>

            {/* Add Journey Button */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
              <DialogTrigger asChild>
                <Button className="w-full glass text-white font-semibold py-3 sm:py-4 rounded-xl sm:rounded-2xl">
                  <FiPlus className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  Add Journey
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-lg mx-4 max-h-[90vh] overflow-y-auto rounded-3xl border-2 border-gray-200">
                <JourneyForm onSubmit={handleJourneySubmit} />
              </DialogContent>
            </Dialog>
          </div>

          {/* Chart Card */}
          <div className="card-glass">
            <ShadcnLineChart data={consumptionData} />
          </div>

          {/* Journey History */}
          <JourneyHistory journeys={journeys} />

          {/* Leaderboard Card */}
          <div className="card-glass p-4 sm:p-6">
            <Leaderboard users={leaderboardData} />
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block p-6 xl:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Enhanced Header */}
          <div className="mb-8">
            <div className="card-glass p-8 relative overflow-hidden">
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-emerald-500/10 to-green-600/10 rounded-full blur-3xl -translate-y-32 translate-x-32"></div>

              <div className="relative flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-6 mb-4">
                    <Avatar className="w-16 h-16 border-3 border-emerald-200 shadow-glow-emerald">
                      <AvatarImage src="/avatars/sarah.jpg" alt="Sarah" />
                      <AvatarFallback className="bg-emerald-500 text-white font-bold text-lg">S</AvatarFallback>
                    </Avatar>
                    <div>
                      <h1 className="text-3xl xl:text-4xl font-bold text-gray-800 mb-1">Hello, Sarah</h1>
                      <p className="text-gray-600 text-lg">Your emission tracking is going well 🌱</p>
                    </div>
                  </div>

                  {/* Quick stats in header */}
                  <div className="flex items-center gap-8">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">Current: <span className="font-semibold text-gray-800">{currentEmissions}kg CO₂</span></span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">Target: <span className="font-semibold text-gray-800">200kg CO₂</span></span>
                    </div>
                  </div>
                </div>

                {/* Action button in header */}
                <div className="ml-8">
                  <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                    <DialogTrigger asChild>
                      <Button className="bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold px-8 py-4 rounded-2xl text-lg shadow-glow-emerald transition-all duration-300 hover:scale-105">
                        <FiPlus className="w-5 h-5 mr-3" />
                        Add Journey
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto rounded-3xl border-2 border-gray-200">
                      <JourneyForm onSubmit={handleJourneySubmit} />
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-12 gap-6 xl:gap-8">
            {/* Left Panel - Gauge and Key Stats */}
            <div className="col-span-5 space-y-6">
              {/* Main Gauge Card */}
              <div className="bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl p-6 xl:p-8 text-white shadow-2xl shadow-emerald-500/25 relative overflow-hidden">
                {/* Background decoration */}
                <div className="absolute -top-20 -right-20 w-40 h-40 bg-white/10 rounded-full blur-2xl"></div>
                <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-white/5 rounded-full blur-xl"></div>

                <div className="relative">
                  <div className="text-center mb-4">
                    <h2 className="text-xl xl:text-2xl font-bold mb-1">Monthly Progress</h2>
                    <p className="text-emerald-100 text-sm">Track your carbon footprint</p>
                  </div>

                  {/* Compact Gauge */}
                  <div className="flex justify-center mb-4">
                    <ModernGauge value={currentEmissions} max={200} size={220} />
                  </div>
                </div>
              </div>

              {/* Enhanced Stats Cards */}
              <div className="grid grid-cols-2 gap-4">
                <div className="card-glass p-6 relative overflow-hidden group hover:shadow-lg transition-all duration-300">
                  <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-amber-400 to-amber-600"></div>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-2">
                      <div className="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center">
                        <div className="w-4 h-4 bg-amber-500 rounded-full"></div>
                      </div>
                      <span className="text-xs text-gray-500 font-medium">PROGRESS</span>
                    </div>
                    <div className="text-2xl xl:text-3xl font-bold text-amber-600 mb-1">{Math.round((currentEmissions / 200) * 100)}%</div>
                    <div className="text-sm text-gray-600">Monthly Target</div>
                  </div>
                </div>

                <div className="card-glass p-6 relative overflow-hidden group hover:shadow-lg transition-all duration-300">
                  <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-emerald-400 to-emerald-600"></div>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-2">
                      <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                        <div className="w-4 h-4 bg-emerald-500 rounded-full"></div>
                      </div>
                      <span className="text-xs text-gray-500 font-medium">REMAINING</span>
                    </div>
                    <div className="text-2xl xl:text-3xl font-bold text-emerald-600 mb-1">{200 - currentEmissions}</div>
                    <div className="text-sm text-gray-600">kg CO₂ Left</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Panel - Charts and Journey History */}
            <div className="col-span-7 space-y-6">
              {/* Enhanced Chart Card */}
              <div className="card-glass p-6 xl:p-8 h-80">
                <div className="mb-4">
                  <h3 className="text-lg xl:text-xl font-semibold text-gray-800 mb-1">Emission Trends</h3>
                  <p className="text-sm text-gray-600">Your carbon footprint over time</p>
                </div>
                <div className="h-64">
                  <ShadcnLineChart data={consumptionData} />
                </div>
              </div>

              {/* Journey History Card */}
              <div className="h-96">
                <JourneyHistory journeys={journeys} />
              </div>
            </div>

            {/* Bottom Panel - Enhanced Leaderboard */}
            <div className="col-span-12 mt-6">
              <div className="card-glass p-6 xl:p-8 relative overflow-hidden">
                {/* Background decoration */}
                <div className="absolute top-0 right-0 w-48 h-48 bg-gradient-to-br from-blue-500/5 to-purple-600/5 rounded-full blur-3xl"></div>

                <div className="relative">
                  <div className="mb-6">
                    <h3 className="text-xl xl:text-2xl font-semibold text-gray-800 mb-2">Community Leaderboard</h3>
                    <p className="text-gray-600">See how you compare with other eco-warriors</p>
                  </div>
                  <Leaderboard users={leaderboardData} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
