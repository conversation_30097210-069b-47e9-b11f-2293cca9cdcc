"use client"

import React, { useState, useMemo } from 'react'
import { ModernGauge } from '@/components/modern-gauge'
import { ShadcnLineChart } from '@/components/shadcn-line-chart'
import { Leaderboard } from '@/components/leaderboard'
import { JourneyForm } from '@/components/journey-form'
import { JourneyHistory } from '@/components/journey-history'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { FiPlus } from 'react-icons/fi'
import { Journey } from '@/types/journey'
import { generateMockJourneys, transportModes } from '@/lib/mock-journeys'

export default function Home() {
  const [currentEmissions, setCurrentEmissions] = useState(120)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [journeys, setJourneys] = useState<Journey[]>(() => generateMockJourneys())

  // Sample data
  const consumptionData = [
    { month: 'Jan', value: 520 },
    { month: 'Feb', value: 480 },
    { month: 'Mar', value: 620 },
    { month: 'Apr', value: 380 },
    { month: 'May', value: 450 },
    { month: 'Jun', value: 590 },
    { month: 'Jul', value: 680 },
    { month: 'Aug', value: 520 },
    { month: 'Sep', value: 380 },
    { month: 'Oct', value: 680 },
    { month: 'Nov', value: 590 },
    { month: 'Dec', value: 740 },
  ]

  const leaderboardData = [
    { id: 1, name: 'Anita', emissions: 450, avatar: '/avatars/anita.jpg' },
    { id: 2, name: 'Diana', emissions: 464, avatar: '/avatars/diana.jpg' },
    { id: 3, name: 'Lucia', emissions: 532, avatar: '/avatars/lucia.jpg' },
    { id: 4, name: 'Oliver Deak', emissions: 740, avatar: '/avatars/oliver.jpg' },
  ]

  const handleJourneySubmit = (data: { transport: string; mileage: number; type: string; emissions: number }) => {
    const transportMode = transportModes.find(mode => mode.id === data.transport)

    if (transportMode) {
      const newJourney: Journey = {
        id: `journey-new-${journeys.length}`,
        transportMode: data.transport,
        transportName: transportMode.name,
        transportIcon: transportMode.icon,
        transportColor: transportMode.color,
        distance: data.mileage,
        emissions: data.emissions === 0 ? 0 : Number(data.emissions.toFixed(1)),
        timestamp: new Date(),
        vehicleType: data.type
      }

      setJourneys(prev => [newJourney, ...prev])
      setCurrentEmissions(prev => prev + data.emissions)
    }

    setIsModalOpen(false)
  }

  return (
    <div className="min-h-screen bg-mesh-gradient">
      {/* Mobile Layout */}
      <div className="lg:hidden p-4 sm:p-6">
        <div className="max-w-sm sm:max-w-md mx-auto space-y-4 sm:space-y-6">
          {/* Header Card */}
          <div className="bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl p-4 sm:p-6 text-white shadow-xl shadow-emerald-500/20">
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <div>
                <h1 className="text-xl sm:text-2xl font-bold">Hello, Sarah</h1>
                <p className="text-emerald-100 text-xs sm:text-sm">Your emission is going well 🌱</p>
              </div>
              <Avatar className="w-12 h-12 sm:w-14 sm:h-14 border-2 sm:border-3 border-white/30 shadow-lg">
                <AvatarImage src="/avatars/sarah.jpg" alt="Sarah" />
                <AvatarFallback className="bg-emerald-600 text-white font-semibold text-sm sm:text-base">S</AvatarFallback>
              </Avatar>
            </div>

            {/* Gauge Section */}
            <div className="flex justify-center mb-4 sm:mb-6">
              <ModernGauge value={currentEmissions} max={200} size={160} />
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6">
              <div className="glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-white">{Math.round((currentEmissions / 200) * 100)}%</div>
                <div className="text-xs text-emerald-100">Monthly Progress</div>
              </div>
              <div className="glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-white">{200 - currentEmissions}</div>
                <div className="text-xs text-emerald-100">kg Remaining</div>
              </div>
            </div>

            {/* Add Journey Button */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
              <DialogTrigger asChild>
                <Button className="w-full glass text-white font-semibold py-3 sm:py-4 rounded-xl sm:rounded-2xl">
                  <FiPlus className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  Add Journey
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-lg mx-4 max-h-[90vh] overflow-y-auto rounded-3xl">
                <JourneyForm onSubmit={handleJourneySubmit} />
              </DialogContent>
            </Dialog>
          </div>

          {/* Chart Card */}
          <div className="card-glass">
            <ShadcnLineChart data={consumptionData} />
          </div>

          {/* Journey History */}
          <JourneyHistory journeys={journeys} />

          {/* Leaderboard Card */}
          <div className="card-glass p-4 sm:p-6">
            <Leaderboard users={leaderboardData} />
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block p-6 xl:p-8">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Streamlined Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="w-14 h-14 border-2 border-emerald-200 shadow-lg">
                <AvatarImage src="/avatars/sarah.jpg" alt="Sarah" />
                <AvatarFallback className="bg-emerald-500 text-white font-bold">S</AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">Hello, Sarah</h1>
                <p className="text-gray-600">Your emission tracking is going well 🌱</p>
              </div>
            </div>

            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold px-6 py-3 rounded-xl shadow-lg transition-all duration-300 hover:scale-105">
                  <FiPlus className="w-5 h-5 mr-2" />
                  Add Journey
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto rounded-3xl">
                <JourneyForm onSubmit={handleJourneySubmit} />
              </DialogContent>
            </Dialog>
          </div>

          {/* Key Metrics Row */}
          <div className="grid grid-cols-4 gap-6">
            {/* Current Emissions */}
            <div className="card-glass p-6 relative overflow-hidden group hover:shadow-lg transition-all duration-300">
              <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-blue-400 to-blue-600"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                    <div className="w-5 h-5 bg-blue-500 rounded-full"></div>
                  </div>
                  <span className="text-xs text-gray-500 font-medium">CURRENT</span>
                </div>
                <div className="text-3xl font-bold text-blue-600 mb-1">{currentEmissions}</div>
                <div className="text-sm text-gray-600">kg CO₂ Emissions</div>
              </div>
            </div>

            {/* Progress Percentage */}
            <div className="card-glass p-6 relative overflow-hidden group hover:shadow-lg transition-all duration-300">
              <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-amber-400 to-amber-600"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-3">
                  <div className="w-10 h-10 bg-amber-100 rounded-xl flex items-center justify-center">
                    <div className="w-5 h-5 bg-amber-500 rounded-full"></div>
                  </div>
                  <span className="text-xs text-gray-500 font-medium">PROGRESS</span>
                </div>
                <div className="text-3xl font-bold text-amber-600 mb-1">{Math.round((currentEmissions / 200) * 100)}%</div>
                <div className="text-sm text-gray-600">of Monthly Target</div>
              </div>
            </div>

            {/* Remaining */}
            <div className="card-glass p-6 relative overflow-hidden group hover:shadow-lg transition-all duration-300">
              <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-emerald-400 to-emerald-600"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-3">
                  <div className="w-10 h-10 bg-emerald-100 rounded-xl flex items-center justify-center">
                    <div className="w-5 h-5 bg-emerald-500 rounded-full"></div>
                  </div>
                  <span className="text-xs text-gray-500 font-medium">REMAINING</span>
                </div>
                <div className="text-3xl font-bold text-emerald-600 mb-1">{200 - currentEmissions}</div>
                <div className="text-sm text-gray-600">kg CO₂ Left</div>
              </div>
            </div>

            {/* Target */}
            <div className="card-glass p-6 relative overflow-hidden group hover:shadow-lg transition-all duration-300">
              <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-purple-400 to-purple-600"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-3">
                  <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                    <div className="w-5 h-5 bg-purple-500 rounded-full"></div>
                  </div>
                  <span className="text-xs text-gray-500 font-medium">TARGET</span>
                </div>
                <div className="text-3xl font-bold text-purple-600 mb-1">200</div>
                <div className="text-sm text-gray-600">kg CO₂ Monthly</div>
              </div>
            </div>
          </div>

          {/* Main Content Row */}
          <div className="grid grid-cols-12 gap-6">
            {/* Chart Section */}
            <div className="col-span-8">
              <div className="card-glass p-6 h-96">
                <div className="mb-4">
                  <h3 className="text-xl font-semibold text-gray-800 mb-1">Emission Trends</h3>
                  <p className="text-sm text-gray-600">Your carbon footprint over time</p>
                </div>
                <div className="h-80">
                  <ShadcnLineChart data={consumptionData} />
                </div>
              </div>
            </div>

            {/* Gauge Section */}
            <div className="col-span-4">
              <div className="bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl p-6 text-white shadow-xl h-96 flex flex-col justify-center items-center relative overflow-hidden">
                <div className="absolute -top-10 -right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl"></div>
                <div className="absolute -bottom-5 -left-5 w-24 h-24 bg-white/5 rounded-full blur-xl"></div>

                <div className="relative text-center">
                  <h3 className="text-lg font-bold mb-2">Monthly Progress</h3>
                  <p className="text-emerald-100 text-sm mb-4">Track your footprint</p>
                  <ModernGauge value={currentEmissions} max={200} size={200} />
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Content Row */}
          <div className="grid grid-cols-12 gap-6">
            {/* Journey History */}
            <div className="col-span-8">
              <JourneyHistory journeys={journeys} />
            </div>

            {/* Leaderboard Summary */}
            <div className="col-span-4">
              <div className="card-glass p-6 h-96">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-800 mb-1">Leaderboard</h3>
                  <p className="text-sm text-gray-600">Top eco-warriors</p>
                </div>
                <div className="h-80 overflow-y-auto">
                  <Leaderboard users={leaderboardData.slice(0, 5)} />
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>
    </div>
  )
}
