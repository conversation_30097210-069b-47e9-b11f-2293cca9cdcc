"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_modern_gauge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/modern-gauge */ \"(app-pages-browser)/./components/modern-gauge.tsx\");\n/* harmony import */ var _components_shadcn_line_chart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shadcn-line-chart */ \"(app-pages-browser)/./components/shadcn-line-chart.tsx\");\n/* harmony import */ var _components_leaderboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/leaderboard */ \"(app-pages-browser)/./components/leaderboard.tsx\");\n/* harmony import */ var _components_journey_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/journey-form */ \"(app-pages-browser)/./components/journey-form.tsx\");\n/* harmony import */ var _components_journey_history__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/journey-history */ \"(app-pages-browser)/./components/journey-history.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_FiPlus_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FiPlus!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_mock_journeys__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/mock-journeys */ \"(app-pages-browser)/./lib/mock-journeys.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [currentEmissions, setCurrentEmissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(120);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [journeys, setJourneys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Home.useState\": ()=>(0,_lib_mock_journeys__WEBPACK_IMPORTED_MODULE_10__.generateMockJourneys)()\n    }[\"Home.useState\"]);\n    // Sample data\n    const consumptionData = [\n        {\n            month: 'Jan',\n            value: 520\n        },\n        {\n            month: 'Feb',\n            value: 480\n        },\n        {\n            month: 'Mar',\n            value: 620\n        },\n        {\n            month: 'Apr',\n            value: 380\n        },\n        {\n            month: 'May',\n            value: 450\n        },\n        {\n            month: 'Jun',\n            value: 590\n        },\n        {\n            month: 'Jul',\n            value: 680\n        },\n        {\n            month: 'Aug',\n            value: 520\n        },\n        {\n            month: 'Sep',\n            value: 380\n        },\n        {\n            month: 'Oct',\n            value: 680\n        },\n        {\n            month: 'Nov',\n            value: 590\n        },\n        {\n            month: 'Dec',\n            value: 740\n        }\n    ];\n    const leaderboardData = [\n        {\n            id: 1,\n            name: 'Anita',\n            emissions: 450,\n            avatar: '/avatars/anita.jpg'\n        },\n        {\n            id: 2,\n            name: 'Diana',\n            emissions: 464,\n            avatar: '/avatars/diana.jpg'\n        },\n        {\n            id: 3,\n            name: 'Lucia',\n            emissions: 532,\n            avatar: '/avatars/lucia.jpg'\n        },\n        {\n            id: 4,\n            name: 'Oliver Deak',\n            emissions: 740,\n            avatar: '/avatars/oliver.jpg'\n        }\n    ];\n    const handleJourneySubmit = (data)=>{\n        const transportMode = _lib_mock_journeys__WEBPACK_IMPORTED_MODULE_10__.transportModes.find((mode)=>mode.id === data.transport);\n        if (transportMode) {\n            const newJourney = {\n                id: \"journey-\".concat(Date.now()),\n                transportMode: data.transport,\n                transportName: transportMode.name,\n                transportIcon: transportMode.icon,\n                transportColor: transportMode.color,\n                distance: data.mileage,\n                emissions: data.emissions,\n                timestamp: new Date(),\n                vehicleType: data.type\n            };\n            setJourneys((prev)=>[\n                    newJourney,\n                    ...prev\n                ]);\n            setCurrentEmissions((prev)=>prev + data.emissions);\n        }\n        setIsModalOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-mesh-gradient\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden p-4 sm:p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-sm sm:max-w-md mx-auto space-y-4 sm:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl p-4 sm:p-6 text-white shadow-xl shadow-emerald-500/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4 sm:mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl sm:text-2xl font-bold\",\n                                                    children: \"Hello, Sarah\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-emerald-100 text-xs sm:text-sm\",\n                                                    children: \"Your emission is going well \\uD83C\\uDF31\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                            className: \"w-12 h-12 sm:w-14 sm:h-14 border-2 sm:border-3 border-white/30 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarImage, {\n                                                    src: \"/avatars/sarah.jpg\",\n                                                    alt: \"Sarah\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarFallback, {\n                                                    className: \"bg-emerald-600 text-white font-semibold text-sm sm:text-base\",\n                                                    children: \"S\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-4 sm:mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modern_gauge__WEBPACK_IMPORTED_MODULE_2__.ModernGauge, {\n                                        value: currentEmissions,\n                                        max: 200,\n                                        size: 160\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl sm:text-2xl font-bold text-white\",\n                                                    children: [\n                                                        Math.round(currentEmissions / 200 * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-emerald-100\",\n                                                    children: \"Monthly Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass rounded-xl sm:rounded-2xl p-3 sm:p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl sm:text-2xl font-bold text-white\",\n                                                    children: 200 - currentEmissions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-emerald-100\",\n                                                    children: \"kg Remaining\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                                    open: isModalOpen,\n                                    onOpenChange: setIsModalOpen,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                className: \"w-full glass text-white font-semibold py-3 sm:py-4 rounded-xl sm:rounded-2xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiPlus_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiPlus, {\n                                                        className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Add Journey\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                            className: \"sm:max-w-lg mx-4 max-h-[90vh] overflow-y-auto rounded-3xl border-2 border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_form__WEBPACK_IMPORTED_MODULE_5__.JourneyForm, {\n                                                onSubmit: handleJourneySubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-glass\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_line_chart__WEBPACK_IMPORTED_MODULE_3__.ShadcnLineChart, {\n                                data: consumptionData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-glass p-4 sm:p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leaderboard__WEBPACK_IMPORTED_MODULE_4__.Leaderboard, {\n                                users: leaderboardData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_history__WEBPACK_IMPORTED_MODULE_6__.JourneyHistory, {\n                            journeys: journeys\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between card-glass p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl font-bold text-gray-800\",\n                                                children: \"Hello, Sarah\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-2 text-lg\",\n                                                children: \"Your emission tracking is going well \\uD83C\\uDF31\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                        className: \"w-20 h-20 border-4 border-emerald-200 shadow-glow-emerald\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarImage, {\n                                                src: \"/avatars/sarah.jpg\",\n                                                alt: \"Sarah\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarFallback, {\n                                                className: \"bg-emerald-500 text-white font-bold text-xl\",\n                                                children: \"S\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-12 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl p-8 text-white shadow-2xl shadow-emerald-500/25 h-full flex flex-col animate-pulse-soft\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold mb-2\",\n                                                        children: \"Monthly Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-emerald-100\",\n                                                        children: \"Track your carbon footprint\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center mb-8 flex-1 items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modern_gauge__WEBPACK_IMPORTED_MODULE_2__.ModernGauge, {\n                                                    value: currentEmissions,\n                                                    max: 200,\n                                                    size: 280\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                                                open: isModalOpen,\n                                                onOpenChange: setIsModalOpen,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                            className: \"w-full glass text-white font-semibold py-4 rounded-2xl text-lg shadow-glow-emerald\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiPlus_react_icons_fi__WEBPACK_IMPORTED_MODULE_11__.FiPlus, {\n                                                                    className: \"w-6 h-6 mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 166,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Add Journey\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                                        className: \"sm:max-w-lg max-h-[90vh] overflow-y-auto rounded-3xl border-2 border-gray-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_form__WEBPACK_IMPORTED_MODULE_5__.JourneyForm, {\n                                                            onSubmit: handleJourneySubmit\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-4 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card-glass p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl font-bold text-emerald-600\",\n                                                                children: [\n                                                                    Math.round(currentEmissions / 200 * 100),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: \"Monthly Progress\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card-glass p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl font-bold text-gray-800\",\n                                                                children: 200 - currentEmissions\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: \"kg Remaining\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-glass\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_line_chart__WEBPACK_IMPORTED_MODULE_3__.ShadcnLineChart, {\n                                                data: consumptionData\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-glass p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leaderboard__WEBPACK_IMPORTED_MODULE_4__.Leaderboard, {\n                                                users: leaderboardData\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_journey_history__WEBPACK_IMPORTED_MODULE_6__.JourneyHistory, {\n                                        journeys: journeys\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\carbontracker\\\\app\\\\page.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"5h/zXXvEFb0Ro3X+OlKvuaTEMyA=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJEO0FBQ0o7QUFDUztBQUNWO0FBQ0M7QUFDTTtBQUNlO0FBQzdCO0FBQzhCO0FBQ3RDO0FBRW1DO0FBRTNELFNBQVNpQjs7SUFDdEIsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHbEIsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDbUIsYUFBYUMsZUFBZSxHQUFHcEIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDcUIsVUFBVUMsWUFBWSxHQUFHdEIsK0NBQVFBO3lCQUFZLElBQU1jLHlFQUFvQkE7O0lBRTlFLGNBQWM7SUFDZCxNQUFNUyxrQkFBa0I7UUFDdEI7WUFBRUMsT0FBTztZQUFPQyxPQUFPO1FBQUk7UUFDM0I7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQUk7UUFDM0I7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQUk7UUFDM0I7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQUk7UUFDM0I7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQUk7UUFDM0I7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQUk7UUFDM0I7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQUk7UUFDM0I7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQUk7UUFDM0I7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQUk7UUFDM0I7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQUk7UUFDM0I7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQUk7UUFDM0I7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQUk7S0FDNUI7SUFFRCxNQUFNQyxrQkFBa0I7UUFDdEI7WUFBRUMsSUFBSTtZQUFHQyxNQUFNO1lBQVNDLFdBQVc7WUFBS0MsUUFBUTtRQUFxQjtRQUNyRTtZQUFFSCxJQUFJO1lBQUdDLE1BQU07WUFBU0MsV0FBVztZQUFLQyxRQUFRO1FBQXFCO1FBQ3JFO1lBQUVILElBQUk7WUFBR0MsTUFBTTtZQUFTQyxXQUFXO1lBQUtDLFFBQVE7UUFBcUI7UUFDckU7WUFBRUgsSUFBSTtZQUFHQyxNQUFNO1lBQWVDLFdBQVc7WUFBS0MsUUFBUTtRQUFzQjtLQUM3RTtJQUVELE1BQU1DLHNCQUFzQixDQUFDQztRQUMzQixNQUFNQyxnQkFBZ0JsQiwrREFBY0EsQ0FBQ21CLElBQUksQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS1IsRUFBRSxLQUFLSyxLQUFLSSxTQUFTO1FBRTVFLElBQUlILGVBQWU7WUFDakIsTUFBTUksYUFBc0I7Z0JBQzFCVixJQUFJLFdBQXNCLE9BQVhXLEtBQUtDLEdBQUc7Z0JBQ3ZCTixlQUFlRCxLQUFLSSxTQUFTO2dCQUM3QkksZUFBZVAsY0FBY0wsSUFBSTtnQkFDakNhLGVBQWVSLGNBQWNTLElBQUk7Z0JBQ2pDQyxnQkFBZ0JWLGNBQWNXLEtBQUs7Z0JBQ25DQyxVQUFVYixLQUFLYyxPQUFPO2dCQUN0QmpCLFdBQVdHLEtBQUtILFNBQVM7Z0JBQ3pCa0IsV0FBVyxJQUFJVDtnQkFDZlUsYUFBYWhCLEtBQUtpQixJQUFJO1lBQ3hCO1lBRUEzQixZQUFZNEIsQ0FBQUEsT0FBUTtvQkFBQ2I7dUJBQWVhO2lCQUFLO1lBQ3pDaEMsb0JBQW9CZ0MsQ0FBQUEsT0FBUUEsT0FBT2xCLEtBQUtILFNBQVM7UUFDbkQ7UUFFQVQsZUFBZTtJQUNqQjtJQUVBLHFCQUNFLDhEQUFDK0I7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7OERBQ0MsOERBQUNFO29EQUFHRCxXQUFVOzhEQUFnQzs7Ozs7OzhEQUM5Qyw4REFBQ0U7b0RBQUVGLFdBQVU7OERBQXNDOzs7Ozs7Ozs7Ozs7c0RBRXJELDhEQUFDOUMseURBQU1BOzRDQUFDOEMsV0FBVTs7OERBQ2hCLDhEQUFDNUMsOERBQVdBO29EQUFDK0MsS0FBSTtvREFBcUJDLEtBQUk7Ozs7Ozs4REFDMUMsOERBQUNqRCxpRUFBY0E7b0RBQUM2QyxXQUFVOzhEQUErRDs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUs3Riw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNuRCxpRUFBV0E7d0NBQUN3QixPQUFPUjt3Q0FBa0J3QyxLQUFLO3dDQUFLQyxNQUFNOzs7Ozs7Ozs7Ozs4Q0FJeEQsOERBQUNQO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7d0RBQTRDTyxLQUFLQyxLQUFLLENBQUMsbUJBQW9CLE1BQU87d0RBQUs7Ozs7Ozs7OERBQ3RHLDhEQUFDVDtvREFBSUMsV0FBVTs4REFBMkI7Ozs7Ozs7Ozs7OztzREFFNUMsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQTRDLE1BQU1uQzs7Ozs7OzhEQUNqRSw4REFBQ2tDO29EQUFJQyxXQUFVOzhEQUEyQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUs5Qyw4REFBQzFDLHlEQUFNQTtvQ0FBQ21ELE1BQU0xQztvQ0FBYTJDLGNBQWMxQzs7c0RBQ3ZDLDhEQUFDUixnRUFBYUE7NENBQUNtRCxPQUFPO3NEQUNwQiw0RUFBQ3RELHlEQUFNQTtnREFBQzJDLFdBQVU7O2tFQUNoQiw4REFBQ3ZDLGlGQUFNQTt3REFBQ3VDLFdBQVU7Ozs7OztvREFBK0I7Ozs7Ozs7Ozs7OztzREFJckQsOERBQUN6QyxnRUFBYUE7NENBQUN5QyxXQUFVO3NEQUN2Qiw0RUFBQ2hELGlFQUFXQTtnREFBQzRELFVBQVVqQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTdCLDhEQUFDb0I7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNsRCwwRUFBZUE7Z0NBQUM4QixNQUFNVDs7Ozs7Ozs7Ozs7c0NBSXpCLDhEQUFDNEI7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNqRCxnRUFBV0E7Z0NBQUM4RCxPQUFPdkM7Ozs7Ozs7Ozs7O3NDQUl0Qiw4REFBQ3JCLHVFQUFjQTs0QkFBQ2dCLFVBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFLOUIsOERBQUM4QjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUNFO2dEQUFHRCxXQUFVOzBEQUFtQzs7Ozs7OzBEQUNqRCw4REFBQ0U7Z0RBQUVGLFdBQVU7MERBQTZCOzs7Ozs7Ozs7Ozs7a0RBRTVDLDhEQUFDOUMseURBQU1BO3dDQUFDOEMsV0FBVTs7MERBQ2hCLDhEQUFDNUMsOERBQVdBO2dEQUFDK0MsS0FBSTtnREFBcUJDLEtBQUk7Ozs7OzswREFDMUMsOERBQUNqRCxpRUFBY0E7Z0RBQUM2QyxXQUFVOzBEQUE4Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzlFLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2M7d0RBQUdkLFdBQVU7a0VBQTBCOzs7Ozs7a0VBQ3hDLDhEQUFDRTt3REFBRUYsV0FBVTtrRUFBbUI7Ozs7Ozs7Ozs7OzswREFJbEMsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDbkQsaUVBQVdBO29EQUFDd0IsT0FBT1I7b0RBQWtCd0MsS0FBSztvREFBS0MsTUFBTTs7Ozs7Ozs7Ozs7MERBSXhELDhEQUFDaEQseURBQU1BO2dEQUFDbUQsTUFBTTFDO2dEQUFhMkMsY0FBYzFDOztrRUFDdkMsOERBQUNSLGdFQUFhQTt3REFBQ21ELE9BQU87a0VBQ3BCLDRFQUFDdEQseURBQU1BOzREQUFDMkMsV0FBVTs7OEVBQ2hCLDhEQUFDdkMsaUZBQU1BO29FQUFDdUMsV0FBVTs7Ozs7O2dFQUFpQjs7Ozs7Ozs7Ozs7O2tFQUl2Qyw4REFBQ3pDLGdFQUFhQTt3REFBQ3lDLFdBQVU7a0VBQ3ZCLDRFQUFDaEQsaUVBQVdBOzREQUFDNEQsVUFBVWpDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU8vQiw4REFBQ29CO29DQUFJQyxXQUFVOztzREFFYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7b0VBQXVDTyxLQUFLQyxLQUFLLENBQUMsbUJBQW9CLE1BQU87b0VBQUs7Ozs7Ozs7MEVBQ2pHLDhEQUFDVDtnRUFBSUMsV0FBVTswRUFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUdoRCw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQW9DLE1BQU1uQzs7Ozs7OzBFQUN6RCw4REFBQ2tDO2dFQUFJQyxXQUFVOzBFQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTWxELDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2xELDBFQUFlQTtnREFBQzhCLE1BQU1UOzs7Ozs7Ozs7OztzREFJekIsOERBQUM0Qjs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2pELGdFQUFXQTtnREFBQzhELE9BQU92Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS3hCLDhEQUFDeUI7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUMvQyx1RUFBY0E7d0NBQUNnQixVQUFVQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU94QztHQXZNd0JMO0tBQUFBIiwic291cmNlcyI6WyJDOlxcY2FyYm9udHJhY2tlclxcYXBwXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZU1lbW8sIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgTW9kZXJuR2F1Z2UgfSBmcm9tICdAL2NvbXBvbmVudHMvbW9kZXJuLWdhdWdlJ1xuaW1wb3J0IHsgU2hhZGNuTGluZUNoYXJ0IH0gZnJvbSAnQC9jb21wb25lbnRzL3NoYWRjbi1saW5lLWNoYXJ0J1xuaW1wb3J0IHsgTGVhZGVyYm9hcmQgfSBmcm9tICdAL2NvbXBvbmVudHMvbGVhZGVyYm9hcmQnXG5pbXBvcnQgeyBKb3VybmV5Rm9ybSB9IGZyb20gJ0AvY29tcG9uZW50cy9qb3VybmV5LWZvcm0nXG5pbXBvcnQgeyBKb3VybmV5SGlzdG9yeSB9IGZyb20gJ0AvY29tcG9uZW50cy9qb3VybmV5LWhpc3RvcnknXG5pbXBvcnQgeyBBdmF0YXIsIEF2YXRhckZhbGxiYWNrLCBBdmF0YXJJbWFnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9hdmF0YXInXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgRGlhbG9nLCBEaWFsb2dDb250ZW50LCBEaWFsb2dUcmlnZ2VyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2RpYWxvZydcbmltcG9ydCB7IEZpUGx1cyB9IGZyb20gJ3JlYWN0LWljb25zL2ZpJ1xuaW1wb3J0IHsgSm91cm5leSB9IGZyb20gJ0AvdHlwZXMvam91cm5leSdcbmltcG9ydCB7IGdlbmVyYXRlTW9ja0pvdXJuZXlzLCB0cmFuc3BvcnRNb2RlcyB9IGZyb20gJ0AvbGliL21vY2stam91cm5leXMnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIGNvbnN0IFtjdXJyZW50RW1pc3Npb25zLCBzZXRDdXJyZW50RW1pc3Npb25zXSA9IHVzZVN0YXRlKDEyMClcbiAgY29uc3QgW2lzTW9kYWxPcGVuLCBzZXRJc01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2pvdXJuZXlzLCBzZXRKb3VybmV5c10gPSB1c2VTdGF0ZTxKb3VybmV5W10+KCgpID0+IGdlbmVyYXRlTW9ja0pvdXJuZXlzKCkpXG5cbiAgLy8gU2FtcGxlIGRhdGFcbiAgY29uc3QgY29uc3VtcHRpb25EYXRhID0gW1xuICAgIHsgbW9udGg6ICdKYW4nLCB2YWx1ZTogNTIwIH0sXG4gICAgeyBtb250aDogJ0ZlYicsIHZhbHVlOiA0ODAgfSxcbiAgICB7IG1vbnRoOiAnTWFyJywgdmFsdWU6IDYyMCB9LFxuICAgIHsgbW9udGg6ICdBcHInLCB2YWx1ZTogMzgwIH0sXG4gICAgeyBtb250aDogJ01heScsIHZhbHVlOiA0NTAgfSxcbiAgICB7IG1vbnRoOiAnSnVuJywgdmFsdWU6IDU5MCB9LFxuICAgIHsgbW9udGg6ICdKdWwnLCB2YWx1ZTogNjgwIH0sXG4gICAgeyBtb250aDogJ0F1ZycsIHZhbHVlOiA1MjAgfSxcbiAgICB7IG1vbnRoOiAnU2VwJywgdmFsdWU6IDM4MCB9LFxuICAgIHsgbW9udGg6ICdPY3QnLCB2YWx1ZTogNjgwIH0sXG4gICAgeyBtb250aDogJ05vdicsIHZhbHVlOiA1OTAgfSxcbiAgICB7IG1vbnRoOiAnRGVjJywgdmFsdWU6IDc0MCB9LFxuICBdXG5cbiAgY29uc3QgbGVhZGVyYm9hcmREYXRhID0gW1xuICAgIHsgaWQ6IDEsIG5hbWU6ICdBbml0YScsIGVtaXNzaW9uczogNDUwLCBhdmF0YXI6ICcvYXZhdGFycy9hbml0YS5qcGcnIH0sXG4gICAgeyBpZDogMiwgbmFtZTogJ0RpYW5hJywgZW1pc3Npb25zOiA0NjQsIGF2YXRhcjogJy9hdmF0YXJzL2RpYW5hLmpwZycgfSxcbiAgICB7IGlkOiAzLCBuYW1lOiAnTHVjaWEnLCBlbWlzc2lvbnM6IDUzMiwgYXZhdGFyOiAnL2F2YXRhcnMvbHVjaWEuanBnJyB9LFxuICAgIHsgaWQ6IDQsIG5hbWU6ICdPbGl2ZXIgRGVhaycsIGVtaXNzaW9uczogNzQwLCBhdmF0YXI6ICcvYXZhdGFycy9vbGl2ZXIuanBnJyB9LFxuICBdXG5cbiAgY29uc3QgaGFuZGxlSm91cm5leVN1Ym1pdCA9IChkYXRhOiB7IHRyYW5zcG9ydDogc3RyaW5nOyBtaWxlYWdlOiBudW1iZXI7IHR5cGU6IHN0cmluZzsgZW1pc3Npb25zOiBudW1iZXIgfSkgPT4ge1xuICAgIGNvbnN0IHRyYW5zcG9ydE1vZGUgPSB0cmFuc3BvcnRNb2Rlcy5maW5kKG1vZGUgPT4gbW9kZS5pZCA9PT0gZGF0YS50cmFuc3BvcnQpXG5cbiAgICBpZiAodHJhbnNwb3J0TW9kZSkge1xuICAgICAgY29uc3QgbmV3Sm91cm5leTogSm91cm5leSA9IHtcbiAgICAgICAgaWQ6IGBqb3VybmV5LSR7RGF0ZS5ub3coKX1gLFxuICAgICAgICB0cmFuc3BvcnRNb2RlOiBkYXRhLnRyYW5zcG9ydCxcbiAgICAgICAgdHJhbnNwb3J0TmFtZTogdHJhbnNwb3J0TW9kZS5uYW1lLFxuICAgICAgICB0cmFuc3BvcnRJY29uOiB0cmFuc3BvcnRNb2RlLmljb24sXG4gICAgICAgIHRyYW5zcG9ydENvbG9yOiB0cmFuc3BvcnRNb2RlLmNvbG9yLFxuICAgICAgICBkaXN0YW5jZTogZGF0YS5taWxlYWdlLFxuICAgICAgICBlbWlzc2lvbnM6IGRhdGEuZW1pc3Npb25zLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICAgIHZlaGljbGVUeXBlOiBkYXRhLnR5cGVcbiAgICAgIH1cblxuICAgICAgc2V0Sm91cm5leXMocHJldiA9PiBbbmV3Sm91cm5leSwgLi4ucHJldl0pXG4gICAgICBzZXRDdXJyZW50RW1pc3Npb25zKHByZXYgPT4gcHJldiArIGRhdGEuZW1pc3Npb25zKVxuICAgIH1cblxuICAgIHNldElzTW9kYWxPcGVuKGZhbHNlKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1tZXNoLWdyYWRpZW50XCI+XG4gICAgICB7LyogTW9iaWxlIExheW91dCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuIHAtNCBzbTpwLTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1zbSBzbTptYXgtdy1tZCBteC1hdXRvIHNwYWNlLXktNCBzbTpzcGFjZS15LTZcIj5cbiAgICAgICAgICB7LyogSGVhZGVyIENhcmQgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLWVtZXJhbGQtNTAwIHRvLWdyZWVuLTYwMCByb3VuZGVkLTN4bCBwLTQgc206cC02IHRleHQtd2hpdGUgc2hhZG93LXhsIHNoYWRvdy1lbWVyYWxkLTUwMC8yMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNCBzbTptYi02XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgc206dGV4dC0yeGwgZm9udC1ib2xkXCI+SGVsbG8sIFNhcmFoPC9oMT5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWVtZXJhbGQtMTAwIHRleHQteHMgc206dGV4dC1zbVwiPllvdXIgZW1pc3Npb24gaXMgZ29pbmcgd2VsbCDwn4yxPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgc206dy0xNCBzbTpoLTE0IGJvcmRlci0yIHNtOmJvcmRlci0zIGJvcmRlci13aGl0ZS8zMCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICA8QXZhdGFySW1hZ2Ugc3JjPVwiL2F2YXRhcnMvc2FyYWguanBnXCIgYWx0PVwiU2FyYWhcIiAvPlxuICAgICAgICAgICAgICAgIDxBdmF0YXJGYWxsYmFjayBjbGFzc05hbWU9XCJiZy1lbWVyYWxkLTYwMCB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgdGV4dC1zbSBzbTp0ZXh0LWJhc2VcIj5TPC9BdmF0YXJGYWxsYmFjaz5cbiAgICAgICAgICAgICAgPC9BdmF0YXI+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEdhdWdlIFNlY3Rpb24gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgbWItNCBzbTptYi02XCI+XG4gICAgICAgICAgICAgIDxNb2Rlcm5HYXVnZSB2YWx1ZT17Y3VycmVudEVtaXNzaW9uc30gbWF4PXsyMDB9IHNpemU9ezE2MH0gLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogUXVpY2sgU3RhdHMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTMgc206Z2FwLTQgbWItNCBzbTptYi02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xhc3Mgcm91bmRlZC14bCBzbTpyb3VuZGVkLTJ4bCBwLTMgc206cC00IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhsIHNtOnRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+e01hdGgucm91bmQoKGN1cnJlbnRFbWlzc2lvbnMgLyAyMDApICogMTAwKX0lPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZW1lcmFsZC0xMDBcIj5Nb250aGx5IFByb2dyZXNzPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzIHJvdW5kZWQteGwgc206cm91bmRlZC0yeGwgcC0zIHNtOnAtNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14bCBzbTp0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPnsyMDAgLSBjdXJyZW50RW1pc3Npb25zfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWVtZXJhbGQtMTAwXCI+a2cgUmVtYWluaW5nPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBBZGQgSm91cm5leSBCdXR0b24gKi99XG4gICAgICAgICAgICA8RGlhbG9nIG9wZW49e2lzTW9kYWxPcGVufSBvbk9wZW5DaGFuZ2U9e3NldElzTW9kYWxPcGVufT5cbiAgICAgICAgICAgICAgPERpYWxvZ1RyaWdnZXIgYXNDaGlsZD5cbiAgICAgICAgICAgICAgICA8QnV0dG9uIGNsYXNzTmFtZT1cInctZnVsbCBnbGFzcyB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgcHktMyBzbTpweS00IHJvdW5kZWQteGwgc206cm91bmRlZC0yeGxcIj5cbiAgICAgICAgICAgICAgICAgIDxGaVBsdXMgY2xhc3NOYW1lPVwidy00IGgtNCBzbTp3LTUgc206aC01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgQWRkIEpvdXJuZXlcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9EaWFsb2dUcmlnZ2VyPlxuICAgICAgICAgICAgICA8RGlhbG9nQ29udGVudCBjbGFzc05hbWU9XCJzbTptYXgtdy1sZyBteC00IG1heC1oLVs5MHZoXSBvdmVyZmxvdy15LWF1dG8gcm91bmRlZC0zeGwgYm9yZGVyLTIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgPEpvdXJuZXlGb3JtIG9uU3VibWl0PXtoYW5kbGVKb3VybmV5U3VibWl0fSAvPlxuICAgICAgICAgICAgICA8L0RpYWxvZ0NvbnRlbnQ+XG4gICAgICAgICAgICA8L0RpYWxvZz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBDaGFydCBDYXJkICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1nbGFzc1wiPlxuICAgICAgICAgICAgPFNoYWRjbkxpbmVDaGFydCBkYXRhPXtjb25zdW1wdGlvbkRhdGF9IC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTGVhZGVyYm9hcmQgQ2FyZCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtZ2xhc3MgcC00IHNtOnAtNlwiPlxuICAgICAgICAgICAgPExlYWRlcmJvYXJkIHVzZXJzPXtsZWFkZXJib2FyZERhdGF9IC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogSm91cm5leSBIaXN0b3J5ICovfVxuICAgICAgICAgIDxKb3VybmV5SGlzdG9yeSBqb3VybmV5cz17am91cm5leXN9IC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBEZXNrdG9wIExheW91dCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmJsb2NrIHAtOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvXCI+XG4gICAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGNhcmQtZ2xhc3MgcC02XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwXCI+SGVsbG8sIFNhcmFoPC9oMT5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTIgdGV4dC1sZ1wiPllvdXIgZW1pc3Npb24gdHJhY2tpbmcgaXMgZ29pbmcgd2VsbCDwn4yxPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJ3LTIwIGgtMjAgYm9yZGVyLTQgYm9yZGVyLWVtZXJhbGQtMjAwIHNoYWRvdy1nbG93LWVtZXJhbGRcIj5cbiAgICAgICAgICAgICAgICA8QXZhdGFySW1hZ2Ugc3JjPVwiL2F2YXRhcnMvc2FyYWguanBnXCIgYWx0PVwiU2FyYWhcIiAvPlxuICAgICAgICAgICAgICAgIDxBdmF0YXJGYWxsYmFjayBjbGFzc05hbWU9XCJiZy1lbWVyYWxkLTUwMCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LXhsXCI+UzwvQXZhdGFyRmFsbGJhY2s+XG4gICAgICAgICAgICAgIDwvQXZhdGFyPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEyIGdhcC04XCI+XG4gICAgICAgICAgICB7LyogTGVmdCBQYW5lbCAtIE1haW4gR2F1Z2UgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLWVtZXJhbGQtNTAwIHRvLWdyZWVuLTYwMCByb3VuZGVkLTN4bCBwLTggdGV4dC13aGl0ZSBzaGFkb3ctMnhsIHNoYWRvdy1lbWVyYWxkLTUwMC8yNSBoLWZ1bGwgZmxleCBmbGV4LWNvbCBhbmltYXRlLXB1bHNlLXNvZnRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgbWItMlwiPk1vbnRobHkgUHJvZ3Jlc3M8L2gyPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1lbWVyYWxkLTEwMFwiPlRyYWNrIHlvdXIgY2FyYm9uIGZvb3RwcmludDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBNb2Rlcm4gR2F1Z2UgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIG1iLTggZmxleC0xIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPE1vZGVybkdhdWdlIHZhbHVlPXtjdXJyZW50RW1pc3Npb25zfSBtYXg9ezIwMH0gc2l6ZT17MjgwfSAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIEFkZCBKb3VybmV5IEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgICA8RGlhbG9nIG9wZW49e2lzTW9kYWxPcGVufSBvbk9wZW5DaGFuZ2U9e3NldElzTW9kYWxPcGVufT5cbiAgICAgICAgICAgICAgICAgIDxEaWFsb2dUcmlnZ2VyIGFzQ2hpbGQ+XG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b24gY2xhc3NOYW1lPVwidy1mdWxsIGdsYXNzIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBweS00IHJvdW5kZWQtMnhsIHRleHQtbGcgc2hhZG93LWdsb3ctZW1lcmFsZFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxGaVBsdXMgY2xhc3NOYW1lPVwidy02IGgtNiBtci0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBBZGQgSm91cm5leVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvRGlhbG9nVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cInNtOm1heC13LWxnIG1heC1oLVs5MHZoXSBvdmVyZmxvdy15LWF1dG8gcm91bmRlZC0zeGwgYm9yZGVyLTIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxKb3VybmV5Rm9ybSBvblN1Ym1pdD17aGFuZGxlSm91cm5leVN1Ym1pdH0gLz5cbiAgICAgICAgICAgICAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICAgICAgICAgICAgICA8L0RpYWxvZz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIE1pZGRsZSBQYW5lbCAtIENoYXJ0cyBhbmQgU3RhdHMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTQgc3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIHsvKiBRdWljayBTdGF0cyBSb3cgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1nbGFzcyBwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1lbWVyYWxkLTYwMFwiPntNYXRoLnJvdW5kKChjdXJyZW50RW1pc3Npb25zIC8gMjAwKSAqIDEwMCl9JTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBtdC0xXCI+TW9udGhseSBQcm9ncmVzczwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWdsYXNzIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwXCI+ezIwMCAtIGN1cnJlbnRFbWlzc2lvbnN9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG10LTFcIj5rZyBSZW1haW5pbmc8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogQ2hhcnQgQ2FyZCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWdsYXNzXCI+XG4gICAgICAgICAgICAgICAgPFNoYWRjbkxpbmVDaGFydCBkYXRhPXtjb25zdW1wdGlvbkRhdGF9IC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBMZWFkZXJib2FyZCBDYXJkICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtZ2xhc3MgcC02XCI+XG4gICAgICAgICAgICAgICAgPExlYWRlcmJvYXJkIHVzZXJzPXtsZWFkZXJib2FyZERhdGF9IC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBSaWdodCBQYW5lbCAtIEpvdXJuZXkgSGlzdG9yeSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tNFwiPlxuICAgICAgICAgICAgICA8Sm91cm5leUhpc3Rvcnkgam91cm5leXM9e2pvdXJuZXlzfSAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiTW9kZXJuR2F1Z2UiLCJTaGFkY25MaW5lQ2hhcnQiLCJMZWFkZXJib2FyZCIsIkpvdXJuZXlGb3JtIiwiSm91cm5leUhpc3RvcnkiLCJBdmF0YXIiLCJBdmF0YXJGYWxsYmFjayIsIkF2YXRhckltYWdlIiwiQnV0dG9uIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ1RyaWdnZXIiLCJGaVBsdXMiLCJnZW5lcmF0ZU1vY2tKb3VybmV5cyIsInRyYW5zcG9ydE1vZGVzIiwiSG9tZSIsImN1cnJlbnRFbWlzc2lvbnMiLCJzZXRDdXJyZW50RW1pc3Npb25zIiwiaXNNb2RhbE9wZW4iLCJzZXRJc01vZGFsT3BlbiIsImpvdXJuZXlzIiwic2V0Sm91cm5leXMiLCJjb25zdW1wdGlvbkRhdGEiLCJtb250aCIsInZhbHVlIiwibGVhZGVyYm9hcmREYXRhIiwiaWQiLCJuYW1lIiwiZW1pc3Npb25zIiwiYXZhdGFyIiwiaGFuZGxlSm91cm5leVN1Ym1pdCIsImRhdGEiLCJ0cmFuc3BvcnRNb2RlIiwiZmluZCIsIm1vZGUiLCJ0cmFuc3BvcnQiLCJuZXdKb3VybmV5IiwiRGF0ZSIsIm5vdyIsInRyYW5zcG9ydE5hbWUiLCJ0cmFuc3BvcnRJY29uIiwiaWNvbiIsInRyYW5zcG9ydENvbG9yIiwiY29sb3IiLCJkaXN0YW5jZSIsIm1pbGVhZ2UiLCJ0aW1lc3RhbXAiLCJ2ZWhpY2xlVHlwZSIsInR5cGUiLCJwcmV2IiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwic3JjIiwiYWx0IiwibWF4Iiwic2l6ZSIsIk1hdGgiLCJyb3VuZCIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJhc0NoaWxkIiwib25TdWJtaXQiLCJ1c2VycyIsImgyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});