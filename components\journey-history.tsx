"use client"

import React, { useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Journey, TimePeriod } from '@/types/journey'
import { filterJourneysByPeriod, calculateJourneyStats, formatDateForPeriod } from '@/lib/mock-journeys'
import { FiChevronLeft, FiChevronRight, FiCalendar, FiActivity } from 'react-icons/fi'

interface JourneyHistoryProps {
  journeys: Journey[]
  className?: string
}

export function JourneyHistory({ journeys, className = "" }: JourneyHistoryProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('daily')
  const [currentDate, setCurrentDate] = useState(new Date())

  const periodButtons: { period: TimePeriod; label: string }[] = [
    { period: 'daily', label: 'Daily' },
    { period: 'weekly', label: 'Weekly' },
    { period: 'monthly', label: 'Monthly' },
    { period: 'yearly', label: 'Yearly' },
  ]

  const filteredJourneys = useMemo(() => 
    filterJourneysByPeriod(journeys, selectedPeriod, currentDate),
    [journeys, selectedPeriod, currentDate]
  )

  const stats = useMemo(() => 
    calculateJourneyStats(filteredJourneys),
    [filteredJourneys]
  )

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    
    switch (selectedPeriod) {
      case 'daily':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1))
        break
      case 'weekly':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7))
        break
      case 'monthly':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1))
        break
      case 'yearly':
        newDate.setFullYear(newDate.getFullYear() + (direction === 'next' ? 1 : -1))
        break
    }
    
    setCurrentDate(newDate)
  }

  const formatJourneyTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    })
  }

  const formatJourneyDate = (timestamp: Date) => {
    if (selectedPeriod === 'daily') {
      return formatJourneyTime(timestamp)
    }
    return timestamp.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      ...(selectedPeriod === 'yearly' ? { year: 'numeric' } : {})
    })
  }

  const formatEmissions = (emissions: number) => {
    if (emissions === 0) return '0'
    if (emissions < 0.1) return '<0.1'
    return emissions.toFixed(1)
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Period Selection */}
      <div className="card-glass p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-2">
            <FiCalendar className="w-5 h-5 text-emerald-600" />
            <h2 className="text-xl font-bold text-gray-800">Journey History</h2>
          </div>
          
          {/* Period Selection Buttons */}
          <div className="flex flex-wrap gap-2">
            {periodButtons.map(({ period, label }) => (
              <Button
                key={period}
                variant={selectedPeriod === period ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedPeriod(period)}
                className={`${
                  selectedPeriod === period 
                    ? 'bg-emerald-500 text-white' 
                    : 'bg-white/60 text-gray-700 border-gray-300'
                }`}
              >
                {label}
              </Button>
            ))}
          </div>
        </div>

        {/* Date Navigation */}
        <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateDate('prev')}
            className="bg-white/60 border-gray-300"
          >
            <FiChevronLeft className="w-4 h-4" />
          </Button>
          
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-800">
              {formatDateForPeriod(currentDate, selectedPeriod)}
            </h3>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateDate('next')}
            className="bg-white/60 border-gray-300"
          >
            <FiChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Statistics Summary */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
        <div className="card-glass p-4 text-center border-l-4 border-emerald-500">
          <div className="text-2xl font-bold text-emerald-600">{stats.totalJourneys}</div>
          <div className="text-sm text-gray-600">Journeys</div>
        </div>
        <div className="card-glass p-4 text-center border-l-4 border-blue-500">
          <div className="text-2xl font-bold text-blue-600">{stats.totalDistance}</div>
          <div className="text-sm text-gray-600">km Total</div>
        </div>
        <div className="card-glass p-4 text-center border-l-4 border-amber-500">
          <div className="text-2xl font-bold text-amber-600">{formatEmissions(stats.totalEmissions)}</div>
          <div className="text-sm text-gray-600">kg CO₂</div>
        </div>
        <div className="card-glass p-4 text-center border-l-4 border-violet-500">
          <div className="text-2xl font-bold text-violet-600">{formatEmissions(stats.averageEmissions)}</div>
          <div className="text-sm text-gray-600">Avg CO₂</div>
        </div>
      </div>

      {/* Journey List */}
      <div className="card-glass p-4 sm:p-6">
        <div className="flex items-center gap-2 mb-4">
          <FiActivity className="w-5 h-5 text-emerald-600" />
          <h3 className="text-lg font-semibold text-gray-800">
            {filteredJourneys.length} Journey{filteredJourneys.length !== 1 ? 's' : ''}
          </h3>
        </div>
        
        {filteredJourneys.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">🌱</div>
            <p>No journeys recorded for this period</p>
            <p className="text-sm">Add a journey to start tracking!</p>
          </div>
        ) : (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {filteredJourneys.map((journey) => (
              <div
                key={journey.id}
                className="flex items-center justify-between p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm"
              >
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <div className={`w-10 h-10 rounded-full ${journey.transportColor} flex items-center justify-center text-white text-lg flex-shrink-0`}>
                    {journey.transportIcon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-800">{journey.transportName}</span>
                      <span className="text-sm text-gray-500">•</span>
                      <span className="text-sm text-gray-600">{journey.distance} km</span>
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatJourneyDate(journey.timestamp)}
                    </div>
                  </div>
                </div>
                <div className="text-right flex-shrink-0 ml-2">
                  <div className="text-sm font-semibold text-emerald-600">
                    {formatEmissions(journey.emissions)} kg
                  </div>
                  <div className="text-xs text-gray-500">CO₂</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
